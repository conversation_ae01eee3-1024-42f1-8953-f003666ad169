<div>
    <div class="card">
        <div class="card-header">
            <div class="row">
                <div class="col-md-6 d-flex justify-content-start gap-3">
                    <div class="form-group">
                        <label for="search">Search</label>
                        <input type="search" class="form-control" id="search" wire:model.live="search">
                    </div>
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select class="form-control" id="status" wire:model.live="assetStatus">
                            <option value="">All</option>
                            <option value="Active">Active</option>
                            <option value="Disbursed">Disbursed</option>
                            <option value="Reposessed">Reposessed</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr class="">
                            <th>Loan #</th>
                            <th>Customer</th>
                            <th class="text-end">Phone Number</th>
                            <th>Identification</th>
                            <th>Location</th>
                            <th>Status</th>
                            <th class="text-end">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($records as $record)
                            <tr>
                                <td>{{ $record->loan->id }}</td>
                                <td>{{ $record->customer->name }}</td>
                                <td class="text-end">{{ $record->customer->Telephone_Number }}</td>
                                <td>{{ $record->Identification }}</td>
                                <td>{{ $record->location }}</td>
                                <td>{{ $record->Status }}</td>
                                <td class="text-end">


                                    <div class="dropdown">
                                        <button class="btn btn-outline-dark btn-sm dropdown-toggle" type="button"
                                            data-bs-toggle="dropdown" aria-expanded="false">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end dropdown-menu-lg-start">
                                            @if (empty($record->Reposessed_Date))
                                                <li>
                                                    <button
                                                        wire:click="reposess('{{ $record->Identification }}', '{{ $record->loan->id }}')"
                                                        class="dropdown-item">
                                                        <span class="me-2"><svg xmlns="http://www.w3.org/2000/svg"
                                                                width="15" height="15" viewBox="0 0 24 24"
                                                                fill="none" stroke="currentColor" stroke-width="2"
                                                                stroke-linecap="round" stroke-linejoin="round"
                                                                class="lucide lucide-undo2-icon lucide-undo-2">
                                                                <path d="M9 14 4 9l5-5" />
                                                                <path
                                                                    d="M4 9h10.5a5.5 5.5 0 0 1 5.5 5.5a5.5 5.5 0 0 1-5.5 5.5H11" />
                                                            </svg></span>
                                                        <span>Reposess</span>
                                                    </button>
                                                </li>
                                            @endif
                                            <!-- Modal Trigger -->
                                            @if ($record->Status == 'Reposessed')
                                                <li>
                                                    <button
                                                        wire:click="$dispatch('openLoanClosureModal', {loanId: '{{ $record->loan->id }}'})"
                                                        class="dropdown-item">
                                                        <span>Loan Closure</span>
                                                    </button>
                                                </li>
                                            @endif
                                            <li>
                                                <button
                                                    wire:click="refreshLocation('{{ $record->Identification }}', '{{ $record->loan->id }}')"
                                                    class="dropdown-item">
                                                    <span class="me-2"><svg xmlns="http://www.w3.org/2000/svg"
                                                            width="15" height="15" viewBox="0 0 24 24"
                                                            fill="none" stroke="currentColor" stroke-width="2"
                                                            stroke-linecap="round" stroke-linejoin="round"
                                                            class="lucide lucide-refresh-cw-icon lucide-refresh-cw">
                                                            <path
                                                                d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" />
                                                            <path d="M21 3v5h-5" />
                                                            <path
                                                                d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" />
                                                            <path d="M8 16H3v5" />
                                                        </svg></span>
                                                    <span>Refresh Location</span>
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>

            </div>
        </div>
    </div>
    @livewire('loan-closure-modal')
</div>
<script>
    document.addEventListener('livewire:initialized', () => {
        Livewire.on('openLoanClosureModal', (event) => {
            // Try to find existing component
            let component = Livewire.find('loan-closure-modal');

            if (!component) {
                // If not found, dynamically add it
                Livewire.dispatch('load-loan-closure-modal');
                component = Livewire.find('loan-closure-modal');
            }

            if (component) {
                component.openModal(event.loanId);
            } else {
                console.error('Failed to load LoanClosureModal component');
            }
        });
    });
</script>
