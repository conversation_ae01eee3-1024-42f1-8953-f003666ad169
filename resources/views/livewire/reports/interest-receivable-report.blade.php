
    <div class="card">
        <div class="card-header ">
            <div class="row">
                <div class="col-md-4">
                    <h5 class="mb-0">Interest Receivable Report</h5>
                </div>
                <div class="col-md-8 d-flex justify-content-end">
                    <x-date-filter/>
                    <x-export-buttons :with-excel="true"/>
                </div>
            </div>
        </div>

        <div class="card-body">
            <table id="report-table" class="table table-bordered">
                <thead>
                    <tr>
                        <th>Loan #</th>
                        <th class="text-start">Customer</th>
                        <th class="text-end">Phone Number</th>
                        <th class="text-start">Loan Amount</th>
                        <th class="text-end">Interest</th>
                        <th class="text-end">Interest Receivable</th>
                        <th class="text-end">Interest Paid</th>
                        <th class="text-end">Interest Accrued</th>
                    </tr>
                </thead>
                <tbody>
                @forelse ($records as $record)
                    <tr>
                        <td>{{ $record->id }}</td>
                        <td>{{ $record->customer->name }}</td>
                        <td class="text-end">{{ $record->customer->Telephone_Number }}</td>
                        <td>{{ number_format($record->Credit_Amount) }}</td>
                        <td class="text-end">{{ number_format($record->schedule_sum_interest) }}</td>
                        <td class="text-end">{{ number_format($record->interest_receivable) }}</td>
                        <td class="text-end">{{ number_format($record->interest_paid) }}</td>
                        <td class="text-end">{{ number_format($record->interest_receivable - $record->interest_paid) }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="6" class="text-center">No records found</td>
                    </tr>
                @endforelse
                </tbody>
                <tfoot>
                    <tr>
                        <th>Totals</th>
                        <th class="text-end">{{ $records->count() }}</th>
                        <th colspan="2"></th>
                        <th class="text-end"><x-money :value="$records->sum('schedule_sum_interest')" /></th>
                        <th class="text-end"><x-money :value="$irSum = $records->sum('interest_receivable')" /></th>
                        <th class="text-end"><x-money :value="$ipSum = $records->sum('interest_paid')" /></th>
                        <th class="text-end"><x-money :value="$irSum - $ipSum" /></th>
                    </tr>
                </tfoot>
            </table>

            <div class="pagination mt-5 d-flex justify-content-end">
                {{ $records->links() }}
            </div>
        </div>
    </div>
