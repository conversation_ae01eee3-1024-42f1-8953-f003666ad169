
    <div class="card">
        <div class="card-header ">
            <div class="row">
                <div class="col-md-4">
                    <h5 class="mb-0">Disbursement Report</h5>
                </div>
                <div class="col-md-8 d-flex justify-content-end">
                    <x-date-filter/>
                    <x-export-buttons :with-excel="true"/>
                </div>
            </div>
        </div>

        <div class="card-body">
            <table id="report-table" class="table table-bordered">
                <thead>
                    <tr>
                        <th>Loan #</th>
                        <th class="text-start">Customer</th>
                        <th class="text-end">Phone Number</th>
                        <th class="text-start"><strong>Product Name</strong></th>
                        <th class="text-end"><strong>Disbursement Date</strong></th>
                        <th class="text-end"><strong>Amount</strong></th>
                    </tr>
                </thead>
                <tbody>
                @forelse ($records as $record)
                    <tr>
                        <td>{{ $record->id }}</td>
                        <td>{{ $record->customer->name }}</td>
                        <td class="text-end">{{ $record->customer->Telephone_Number }}</td>
                        <td>{{ $record->loan_product->Name }}</td>
                        <td class="text-end">{{ $record->created_at->toDateString()}}</td>
                        <td class="text-end"><x-money :value="$record->Facility_Amount_Granted" /></td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="4">No disbursements found</td>
                    </tr>
                @endforelse
                </tbody>
                <tfoot>
                    <tr>
                        <th>Totals</th>
                        <th class="text-end">{{ $records->count() }}</th>
                        <th colspan="3"></th>
                        <th class="text-end"><x-money :value="$records->sum('Facility_Amount_Granted')" /></th>
                    </tr>
                </tfoot>
            </table>

            <div class="pagination mt-5 d-flex justify-content-end">
                {{ $records->links() }}
            </div>
        </div>
    </div>
