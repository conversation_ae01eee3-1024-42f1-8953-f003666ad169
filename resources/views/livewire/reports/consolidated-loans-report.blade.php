<div class="card">
    <div class="card-header ">
        <div class="row">
            <div class="col-md-4">
                <h5 class="mb-0">Consolidated Loans Report</h5>
            </div>
            <div class="col-md-8 d-flex justify-content-end">
                <x-end-date/>
                <x-export-buttons :with-excel="true"/>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table id="report-table" class="table">
                <thead>
                <tr>
                    <th class="text-end">Loan#</th>
                    <th class="text-start" style="min-width: 200px">Customer Name</th>
                    <th class="text-start">Phone Number</th>
                    <th class="text-end">Amount Disbursed</th>
                    <th class="text-end">Date Disbursed</th>
                    <th class="text-end">Maturity Date</th>
                    <th class="text-end">Outstanding Balance</th>
                    <th class="text-end">Interest Rate</th>
                    <th class="text-end">Interest Receivable</th>
                    <th class="text-end">Arrears Days</th>
                    <th class="text-end">Arrears Amount</th>
                    <th class="text-end">Principal Overdue</th>
                    <th class="text-end">Interest Overdue</th>
                    <th class="text-end">Penal Interest Overdue</th>
                    <th class="text-end">Last Payment</th>
                    <th>Gender</th>
                </tr>
                </thead>
                <tbody>
                @forelse($loans as $loan)
                    <tr>
                        <td class="text-end">{{ $loan->id }}</td>
                        <td>{{ $loan->customer->name }}</td>
                        <td>{{ $loan->customer->Telephone_Number }}</td>
                        <td class="text-end">{{ number_format($loan->Facility_Amount_Granted) }}</td>
                        <td class="text-end">{{ $loan->Credit_Account_Date->toDateString() }}</td>
                        <td class="text-end">{{ $loan->Maturity_Date->toDateString() }}</td>
                        <td class="text-end">{{ number_format($loan->schedule_sum_total_outstanding) }}</td>
                        <td class="text-end">{{ $loan->Annual_Interest_Rate_at_Disbursement }}%</td>
                        <td class="text-end">{{ number_format($loan->schedule_sum_interest) }}</td>
                        <td class="text-end">{{ number_format(abs($loan->arrears_days)) }}</td>
                        <td class="text-end">{{ number_format($loan->total_arrears) }}</td>
                        <td class="text-end">{{ number_format($loan->total_principal_overdue) }}</td>
                        <td class="text-end">{{ number_format($loan->total_interest_overdue) }}</td>
                        <td class="text-end">{{ number_format($loan->penalties_overdue) }}</td>
                        <td class="text-end">{{ $loan->last_payment_date?->toDateString() }}</td>
                        <td>{{ $loan->customer->Gender }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="16" class="text-center">No records found</td>
                    </tr>
                @endforelse
                </tbody>
                <tfoot>
                <tr>
                    <th>Totals</th>
                    <th class="text-end">{{ $loans->count() }}</th>
                    <th></th>
                    <th class="text-end">{{ number_format($loans->sum('Facility_Amount_Granted')) }}</th>
                    <th colspan="2"></th>
                    <th class="text-end">{{ number_format($loans->sum('schedule_sum_total_outstanding')) }}</th>
                    <th></th>
                    <th class="text-end">{{ number_format($loans->sum('schedule_sum_interest')) }}</th>
                    <th></th>
                    <th class="text-end">{{ number_format($loans->sum('total_arrears')) }}</th>
                    <th class="text-end">{{ number_format($loans->sum('total_principal_overdue')) }}</th>
                    <th class="text-end">{{ number_format($loans->sum('total_interest_overdue')) }}</th>
                    <th class="text-end">{{ number_format($loans->sum('penalties_overdue')) }}</th>
                    <th colspan="2"></th>
                </tr>
                </tfoot>
            </table>
        </div>
    </div>
    <div class="card-footer">
        {{ $loans->links() }}
    </div>
</div>
