<div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-5">
                        <div class="col-6">
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <div class="ms-3">
                                            <p class="text-muted mb-0 fs-sm">Customer Name</p>
                                            <p class="fw-bold mb-0 fs-6">{{ $loanApplication->customer->full_name }}</p>
                                        </div>
                                    </div>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <div class="ms-3">
                                            <p class="text-muted mb-0 fs-sm">Phone Number</p>
                                            <p class="fw-bold mb-0 fs-6">
                                                {{ $loanApplication->customer->Telephone_Number }}</p>
                                        </div>
                                    </div>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <div class="ms-3">
                                            <p class="text-muted mb-0 fs-sm">Transaction ID</p>
                                            <p class="fw-bold mb-0 fs-6">{{ $loanApplication->downPayment->TXN_ID }}</p>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                        <div class="col-6">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th scope="col">Parameter</th>
                                        <th scope="col">Value</th>
                                    </tr>

                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Application ID</td>
                                        <td>{{ $loanApplication->id }}</td>
                                    </tr>
                                    <tr>
                                        <td>Application Status</td>
                                        <td>{{ $loanApplication->Credit_Application_Status }}</td>
                                    </tr>
                                    @if ($loanApplication->Approval_Reference)
                                        <tr>
                                            <td>Application Approval Reference</td>
                                            <td>{{ $loanApplication->Approval_Reference }}</td>
                                        </tr>
                                    @endif
                                    @if ($loanApplication->Rejection_Reference)
                                        <tr>
                                            <td>Application Rejection Reference</td>
                                            <td>{{ $loanApplication->Rejection_Reference }}</td>
                                        </tr>
                                    @endif
                                    <tr>
                                        <td>Asset Disbursement Status</td>
                                        <td>{{ $loanApplication->downPayment->Asset_Disbursement_Status }}</td>
                                    </tr>
                                    <tr>
                                        <td>Bike Number</td>
                                        <td>{{ $vehicleId ?: 'Not Assigned' }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    @if ($errorMessage)
                        <div class="alert alert-danger text-black">{{ $errorMessage }}</div>
                    @endif
                    <div class="row mb-2">
                        <div class="col-6">
                            <form wire:submit="verify" id="verification-form">
                                <label class="form-label">Customer's NIN e.g CM930121003EGE<span
                                        class="text-danger">*</span></label>
                                <input class="form-control" type="text" wire:model="countryId"
                                    placeholder="Customer NIN" />
                                @error('countryId')
                                    <div class="alert alert-danger text-black">{{ $message }}</div>
                                @enderror
                            </form>
                        </div>
                    </div>

                    <div class="">
                        <button class="btn btn-secondary" type="submit" form="verification-form">Verify</button>
                        <div class="text-info fw-bold pl-4 fs-6" wire:loading wire:target="verify">
                            Verifying NIN, please wait...
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-5">
                @if (!empty($verificationDetails))
                    <div class="col-6 pb-4">
                        <div class="card border-0 shadow-none">
                            <div class="card-body p-0">
                                @if (empty($verificationDetails))
                                    <p>No verification details found</p>
                                @else
                                    <ul class="list-group">
                                        @foreach ($verificationDetails as $key => $detail)
                                            <li
                                                class="list-group-item d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <div class="ms-3">
                                                        <p class="text-muted mb-0 fs-sm">
                                                            {{ str($key)->replace('_', ' ')->upper() }}</p>
                                                        <p class="fw-bold mb-0 fs-6">{{ $detail }}</p>
                                                    </div>
                                                </div>
                                            </li>
                                        @endforeach
                                    </ul>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-6 p-5">
                        @if (!empty($customerFiles))
                            <div class="d-flex justify-content-start fs-5 fw-bold mb-4">
                                <span>Files Uploaded: {{ count($customerFiles) }}</span>
                            </div>
                            <ul class="list-group mb-5">
                                @foreach ($customerFiles as $customerFile)
                                    <li
                                        class="list-group-item border-0 d-flex justify-content-between align-items-center mb-2 bg-white shadow-sm">
                                        <div class="d-flex align-items-center">
                                            <span class="me-2">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-paperclip">
                                                    <path d="M13.234 20.252 21 12.3" />
                                                    <path
                                                        d="m16 6-8.414 8.586a2 2 0 0 0 0 2.828 2 2 0 0 0 2.828 0l8.414-8.586a4 4 0 0 0 0-5.656 4 4 0 0 0-5.656 0l-8.415 8.585a6 6 0 1 0 8.486 8.486" />
                                                </svg>
                                            </span>
                                            <div class="">
                                                <p class="fw-normal mb-0 fs-6">
                                                    {{ str($customerFile)->after($loanApplication->id . '/')->toString() }}
                                                </p>
                                            </div>
                                        </div>
                                        <span class="">
                                            <button class="btn btn-sm btn-outline-danger"
                                                wire:click="removeFile('{{ $customerFile }}')">delete</button>
                                        </span>
                                    </li>
                                @endforeach
                            </ul>

                        @endif
                        <form wire:submit="uploadFiles" enctype="multipart/form-data">
                            @csrf
                            @if (count($requiredFiles) > 0)
                                <h6>Add more files</h6>
                            @endif
                            <div class="mb-4">
                                <select class="form-select" wire:model="selectedFile">
                                    <option value="">Select Document</option>
                                    @foreach ($requiredFiles as $requiredFile)
                                        <option value="{{ $requiredFile }}">
                                            {{ str($requiredFile)->replace('_', ' ')->upper()->toString() }}</option>
                                    @endforeach
                                </select>
                            </div>
                            @if (empty($requiredFiles))
                                <div class="alert alert-success text-black fs-6">All files have been uploaded</div>
                            @endif
                            <div class="mb-4">
                                <label class="form-label">Upload Files</label>
                                <input class="form-control" type="file" wire:model="files"
                                    accept="image/png,image/jpeg,application/pdf">
                            </div>
                            <button type="submit" class="btn btn-secondary">Upload Files</button>
                        </form>

                        <div class="alert alert-secondary fs-6 text-dark mt-4">
                            <p><strong>Caveated Logbook</strong> and <strong>Full Driving Permit</strong> can be
                                uploaded after Loan Asset has been disbursed.</p>
                        </div>
                    </div>
                @else
                    <div class="col-6">
                        <p class="fw-6">No KYC details found.</p>
                    </div>
                @endif
            </div>

            <div class="row mb-5">
                <div class="col-6">
                    @if (
                        !empty($verificationDetails) &&
                            !empty($customerFiles) &&
                            empty($loanApplication->loan) &&
                            $loanApplication->downPayment->canBeSubmitted())
                        <p class="fs-5 py-4 px-5 bg-info-subtle fw-bold">Please confirm that the customer details are
                            correct and that all necessary documents have been uploaded. By confirming,
                            {{ $loanApplication->partner->Institution_Name }} will be notified to approve the loan for
                            {{ $loanApplication->customer->name }}.</p>

                        <div class="mb-5">
                            <label class="form-label">Customer's Bike Number <span class="text-danger">*</span></label>
                            <input class="form-control" type="text" wire:model="vehicleId"
                                placeholder="Bike Number" />
                            @error('vehicleId')
                                <div class="alert alert-danger text-black">{{ $message }}</div>
                            @enderror
                        </div>
                        <!-- Button trigger modal -->
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                            data-bs-target="#submitDownpayment">
                            Submit Documents
                        </button>
                    @endif
                </div>
            </div>

            @if ($loanApplication->downPayment->canBeDisbursed())
                <div class="row mb-5">
                    <div class="col-6">
                        <h4>Disbursement</h4>
                        <ul class="list-group mb-4">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <div class="ms-3">
                                        <p class="text-muted mb-0 fs-sm">Approval Date</p>
                                        <p class="fw-bold mb-0 fs-6">
                                            {{ $loanApplication->downPayment->Approval_Date }}</p>
                                    </div>
                                </div>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <div class="ms-3">
                                        <p class="text-muted mb-0 fs-sm">Approval Reference</p>
                                        <p class="fw-bold mb-0 fs-6">
                                            {{ $loanApplication->downPayment->Approval_Reference }}</p>
                                    </div>
                                </div>
                            </li>
                        </ul>

                        <p class="fs-5 py-4 px-5 bg-info-subtle fw-bold">By disbursing the bike, a
                            {{ $loanApplication->partner->Institution_Name }} loan will be created, and repayments will
                            start tomorrow.</p>
                        <!-- Button trigger modal -->
                        <button type="button" class="btn btn-dark" data-bs-toggle="modal"
                            data-bs-target="#disburseBike">
                            Disburse Bike
                        </button>
                    </div>
                </div>
            @endif
        </div>
    </div>


    <!-- Modals -->

    <div class="modal fade" id="disburseBike" tabindex="-1" aria-labelledby="submit-payment-label"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="submit-payment-label">Confirm submitting down payment</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="fs-6">You are confirming that you have disbursed {{ $this->vehicleId }} to
                        {{ $loanApplication->customer->name }} and that the bike is active. Are you sure about this?
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary me-2"
                        data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-dark" wire:click="disburseBike"
                        onclick="bootstrap.Modal.getInstance(document.getElementById('disburseBike')).hide()">Submit</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="submitDownpayment" tabindex="-1" aria-labelledby="submit-payment-label"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="submit-payment-label">Confirm submitting down payment</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="fs-6">You are about to submit this down payment. Are you sure about this?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary me-2"
                        data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" wire:click="submitDownPayment">Submit</button>
                </div>
            </div>
        </div>
    </div>

</div>
