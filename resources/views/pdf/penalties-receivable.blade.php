@extends('pdf.layouts')

@section('content')
    <div class="text-center">
        <h2 style="margin-bottom: 5px; margin-top: 0; font-size: 16px">{{ $partnerName }}</h2>
        <h4 style="margin-top: 0; margin-bottom: 4px">Penalties Receivable Report</h4>
        <p style="margin-top: 0; font-size: 10px">From: {{ $filters['startDate'] }} to {{ $filters['endDate'] }}</p>
    </div>

    <table id="report-table" class="table table-bordered">
        <thead>
            <tr class="table-header">
                <th>Loan #</th>
                <th class="text-start">Customer</th>
                <th class="text-end">Phone Number</th>
                <th class="text-start">Loan Amount</th>
                <th class="text-end">Penalties</th>
                <th class="text-end">Penalties Receivable</th>
                <th class="text-end">Penalties Paid</th>
                <th class="text-end">Pending Penalties</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($records as $record)
                <tr>
                    <td>{{ $record->id }}</td>
                    <td>{{ $record->customer->name }}</td>
                    <td class="text-end">{{ $record->customer->Telephone_Number }}</td>
                    <td>{{ number_format($record->Credit_Amount) }}</td>
                    <td class="text-end">{{ number_format($record->schedule_sum_penalties) }}</td>
                    <td class="text-end">{{ number_format($record->penaltiesReceivable()) }}</td>
                    <td class="text-end">{{ number_format($record->penaltiesPaid()) }}</td>
                    <td class="text-end">{{ number_format($record->penaltiesReceivable() - $record->penaltiesPaid()) }}
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="6" class="text-center">No records found</td>
                </tr>
            @endforelse
        </tbody>
        <tfoot>
            @php
                $penaltiesReceivableSum = $records->sum(fn($record) => $record->penaltiesReceivable());
                $penaltiesPaidSum = $records->sum(fn($record) => $record->penaltiesPaid());
            @endphp
            <tr>
                <th>Totals</th>
                <th class="text-end">{{ $records->count() }}</th>
                <th colspan="2"></th>
                <th class="text-end"><x-money :value="$records->sum('schedule_sum_penalties')" /></th>
                <th class="text-end">{{ number_format($irSum = $records->sum('penaltiesReceivableSum')) }}</th>
                <th class="text-end">{{ number_format($ipSum = $records->sum('penaltiesPaidSum')) }}</th>
                <th class="text-end">{{ number_format($irSum - $ipSum) }}</th>
            </tr>
        </tfoot>
    </table>
    <x-print-footer />
@endsection
