<?php

namespace App\Actions\Loans;

use App\Enums\LoanAccountType;
use App\Models\JournalEntry;
use App\Models\Loan;
use App\Models\LoanSchedule;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Spatie\ModelStatus\Exceptions\InvalidStatus;

class WriteOffLoanAction
{
    /**
     * @throws Exception
     */
    public function execute(Loan $loan, array $details): Loan
    {
        try {
            DB::transaction(function () use ($loan, $details) {
                if (in_array($loan->Credit_Account_Status, [LoanAccountType::WrittenOff->value, LoanAccountType::PaidOff->value])) {
                    throw new Exception('Loan has already been written off or is fully paid.');
                }

                if (now()->isBefore($loan->Maturity_Date) || $loan->schedule->sum('total_outstanding') === 0) {
                    throw new Exception('This loan is not in arrears.');
                }

                $loanLossProvisionLiabilityAccount = $loan->loan_product->lossProvisionAccount();

                if (empty($loanLossProvisionLiabilityAccount)) {
                    throw new Exception('Loan Loss Provision Account not found.');
                }

                $loan->Credit_Account_Status = LoanAccountType::WrittenOff->value;
                $loan->Last_Status_Change_Date = now()->toDateString();
                // Add: Date Written Off, Written By, Write Off Reason. Do not update Closure Fields
                $loan->Written_Off_Amount = $loan->schedule()->sum('principal_remaining');
                $loan->Written_Off_Date = $details['write_off_date'];
                $loan->Written_Off_Reason = $details['reason'];
                $loan->Written_Off_Officer = auth()->id();
                $loan->save();

                // This will help us track status change history on any model
                $loan->setStatus(LoanAccountType::WrittenOff->name, $loan->Written_Off_Reason);

                // todo: Clear interest in the loan schedule to zero
                // ---- We will use this written off amount to track the balance after a loan has been written off.

                $loan->schedule()->update(['interest_remaining' => 0]);

                // todo: Update any other related fees to zero.

                // todo: Perform write off.

                $txn = [];
                $txn_id = rand(11111, 99999) . "-" . now()->unix();

                // Debit the Loan Loss Provision Account (Liability Account)
                $balanceBeforeUpdate = $loanLossProvisionLiabilityAccount->balance;
                $loanLossProvisionLiabilityAccount->decrement('balance', $loan->Written_Off_Amount);

                $txn[] = [
                    'customer_id' => $loan->Customer_ID,
                    'account_id' => $loanLossProvisionLiabilityAccount->id,
                    'amount' => $loan->Written_Off_Amount,
                    'transactable_id' => $loan->id,
                    'transactable' => Loan::class,
                    'partner_id' => $loan->Partner_ID,
                    'txn_id' => $txn_id,
                    'account_name' => $loanLossProvisionLiabilityAccount->name,
                    'cash_type' => 'Non Cash',
                    'current_balance' => $loanLossProvisionLiabilityAccount->balance,
                    'previous_balance' => $balanceBeforeUpdate,
                    'accounting_type' => 'Debit',
                    'credit_amount' => 0,
                    'debit_amount' => $loan->Written_Off_Amount,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                // Credit the Loan Product Asset Account
                $loan_product_account = $loan->loan_product->general_ledger_account;
                $balanceBeforeUpdate = $loan_product_account->balance;
                $loan_product_account->decrement('balance', $loan->Written_Off_Amount);

                $txn[] = [
                    'customer_id' => $loan->Customer_ID,
                    'account_id' => $loan_product_account->id,
                    'amount' => $loan->Written_Off_Amount,
                    'transactable_id' => $loan->id,
                    'transactable' => Loan::class,
                    'partner_id' => $loan->Partner_ID,
                    'txn_id' => $txn_id,
                    'account_name' => $loan_product_account->name,
                    'cash_type' => 'Non Cash',
                    'current_balance' => $loan_product_account->balance,
                    'previous_balance' => $balanceBeforeUpdate,
                    'accounting_type' => 'Credit',
                    'credit_amount' => $loan->Written_Off_Amount,
                    'debit_amount' => 0,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                JournalEntry::query()->insert($txn);
            });

            return $loan;
        } catch (\Throwable $th) {
            throw new Exception($th->getMessage());
        }
    }
}
