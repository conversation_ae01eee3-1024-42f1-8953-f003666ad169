<?php

namespace App\Actions\Loans;

use App\Enums\LoanApplicationStatus;
use App\Models\Loan;
use App\Models\LoanProductType;
use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class CreateApprovedLoanAction
{
    public function execute(Transaction $transaction): bool
    {
        $transaction->loadMissing(['customer', 'loanApplication']);
        $creditAccountType = LoanProductType::query()->where('name', 'Mobile Loan')->first();
        $loanApplicationSession = $transaction->loanApplication->loan_session;
        $loanApplicationSession->loadMissing('loanProductTerm');
        $loanProductTerm = $loanApplicationSession->loanProductTerm;

        $loan = Loan::query()->create([
            'Partner_ID' => $transaction->Partner_ID,
            'Customer_ID' => $transaction->customer->id,
            'Loan_Product_ID' => $transaction->loanApplication->Loan_Product_ID,
            'Loan_Application_ID' => $transaction->Loan_Application_ID,
            'Credit_Application_Status' => LoanApplicationStatus::Approved->name, // You can dynamically adjust this based on approval logic
            'Credit_Account_Reference' => Loan::generateReference(),
            'Credit_Account_Date' => Carbon::now(),
            'Credit_Amount' => $transaction->loanApplication->Amount,
            'Facility_Amount_Granted' => $transaction->loanApplication->Amount,
            'Credit_Amount_Drawdown' => '0.00', // Confirm this @Najja
            'Credit_Account_Type' => $creditAccountType->Code,
            'Currency' => 'UGX',
            'Maturity_Date' => $loanApplicationSession->Maturity_Date,
            'Annual_Interest_Rate_at_Disbursement' => $loanProductTerm->Interest_Rate,
            'Date_of_First_Payment' => $loanApplicationSession->Date_of_First_Payment,
            'Credit_Amortization_Type' => 1, // Refer to the DSM APPENDIX 1.11
            'Credit_Payment_Frequency' => $loanApplicationSession->Credit_Payment_Frequency ?? "Monthly",
            'Number_of_Payments' => $loanApplicationSession->Number_of_Payments ?? 1,
            'Client_Advice_Notice_Flag' => 'Yes',
            'Term' => $loanProductTerm->Value,
            'Type_of_Interest' => 1, // Refer to the DSM APPENDIX 1.9 0-Fixed, 1-Floating
            'Client_Consent_Flag' => 'Yes',
            'Interest_Rate' => $loanProductTerm->Interest_Rate,
            'Interest_Calculation_Method' => $loanProductTerm->Interest_Calculation_Method,
            'Loan_Term_ID' => $loanProductTerm->id,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);

        $loanApplicationSession->update([
            'Loan_ID' => $loan->id
        ]);
        $transaction->update([
            'Loan_ID' => $loan->id
        ]);

        return true;
    }
}
