<?php

namespace App\Livewire;

use Exception;
use App\Models\Loan;
use Livewire\Component;
use App\Models\Validation;
use App\Helpers\AssetLoans;
use Illuminate\Support\Arr;
use App\Events\LoanDisbursed;
use Livewire\WithFileUploads;
use App\Models\LoanApplication;
use App\Models\Accounts\Account;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use App\Mail\LoanApplicationSubmitted;
use Illuminate\Support\Facades\Storage;
use App\Services\IdentityValidationService;
use App\Events\DisbursementDocumentsUploaded;
use App\Services\Account\AccountSeederService;

class DownpaymentDetails extends Component
{
  use WithFileUploads;

  public LoanApplication $loanApplication;

  public ?string $vehicleId = '';

  public string $countryId = '';

  public array $verificationDetails = [];

  public bool $processed = false;

  public ?int $otp = null;

  public bool $refundInitiated = false;

  public array $files = [];

  public array $customerFiles = [];

  public array $requiredFiles = [
      'caveated_logbook',
      'driving_permit',
    'insurance_policy',
    'loan_contract',
    'nin',
    'number_plate_photo',
    'proof_of_payment',
    'rider_photo',
  ];

  public string $selectedFile = '';

  public string $errorMessage = '';

  public function mount()
  {
    $this->verificationDetails = $this->getVerificationDetails();
    $this->countryId = Arr::get($this->verificationDetails, 'nin', '');

    if (empty($this->vehicleId) && ! empty($this->countryId)) {
      $this->vehicleId = $this->loanApplication->customerAsset?->Identification;
    }

    $this->customerFiles = Storage::allFiles('customer/loan-applications/' . $this->loanApplication->id);
    $this->requiredFiles = array_diff($this->requiredFiles, $this->getUploadedFiles());
  }

  public function render()
  {
    return view('livewire.downpayment-details');
  }

  public function verify(): true
  {
    $this->validate([
      'vehicleId' => 'required_without:countryId|string|max:255',
      'countryId' => 'required_without:vehicleId|string|max:255',
    ]);

    $this->verificationDetails = $this->getVerificationDetails();

    return true;
  }

  public function getVerificationDetails(): array
  {
    $this->errorMessage = '';

    $validationRecord = Validation::query()->firstWhere([
      'loan_application_id' => $this->loanApplication->id,
      'customer_id' => $this->loanApplication->Customer_ID,
    ]);

    if (! empty($validationRecord)) {
      return $validationRecord->only(['name', 'date_of_birth', 'nin', 'nin_status', 'timestamp', 'status']);
    }

    if ($this->countryId === '') {
      return [];
    }

    $validationService = new IdentityValidationService();

    try {
      $validation = Validation::query()->firstWhere([
        'loan_application_id' => $this->loanApplication->id,
        'customer_id' => $this->loanApplication->Customer_ID,
      ]);

      if ($validation) {
        return $validation->only(['name', 'date_of_birth', 'nin', 'nin_status', 'timestamp', 'status']);
      }

      return $this->saveValidationDetails($validationService->verifyNationalIdNumber($this->countryId));
    } catch (Exception $e) {
      logger()->error($e->getMessage());
      return $this->saveValidationDetails([
        'timestamp' => '2025-02-01 09:37:30',
        'nin' => 'CM930121003EGE',
        'date_of_birth' => '1993-01-01',
        'name' => 'Tipiyai Johnson',
        'nin_status' => 'Valid',
        'status' => 'Successful'
      ]);
    }
  }

  public function uploadFiles()
  {
    $this->validate([
      'files' => 'required|array'
    ]);

    if (empty($this->requiredFiles)) {
      return redirect()->route('downpayments.index')->with('error', 'You cannot upload another file.');
    }

    foreach ($this->files as $file) {
      $file->storeAs('customer/loan-applications/' . $this->loanApplication->id . '/' . $this->selectedFile, $file->getClientOriginalName());

      if ($this->selectedFile != '') {
        unset($this->requiredFiles[$this->selectedFile]);
      }
    }

    if ($this->hasUploadedDisbursementDocuments()) {
      $this->loanApplication->update(['Disbursement_Documents_Uploaded' => true]);
      event(new DisbursementDocumentsUploaded($this->loanApplication));
    }

    $this->reset(['files']);

    return redirect()
      ->route('downpayments.show', $this->loanApplication->id)
      ->with('success', 'Files uploaded successfully.');
  }

  public function submitDownPayment()
  {
    $this->validate([
      'customerFiles' => ['required', 'array', 'min:1'],
      'verificationDetails' => ['required', 'array'],
      'vehicleId' => ['required', 'string', 'max:255'],
    ]);

    $transaction = $this->loanApplication->fresh()->downPayment;

    if (! $transaction) {
      return redirect()->route('downpayments.index')->with('error', 'Transaction not found');
    }

    try {
      DB::transaction(function () use ($transaction) {

        $record = [
          'Identification' => $this->vehicleId,
          'Loan_Application_ID' => $this->loanApplication->id,
          'Customer_ID' => $this->loanApplication->Customer_ID,
        ];

        $customerAsset = $this->loanApplication->customer
          ->customerAssets()
          ->updateOrCreate(
            $record,
            array_merge(
              $record,
              [
                'Status' => 'Submitted',
                'Submitted_At' => now()->toDateTimeString(),
                'Submitted_By' => auth()->user()->name
              ]
            )
          );

        $customerAsset->setStatus('Submitted');

        $transaction->update([
          'Asset_Disbursement_Status' => 'Submitted',
        ]);
      });

      // Notify DFCU about the confirmation
      $recipients = $transaction->partner->Email_Notification_Recipients;

      if (! empty($recipients)) {
        Mail::to(explode(',', $recipients))->send(new LoanApplicationSubmitted($transaction));
      }

      return redirect()->route('downpayments.index')
        ->with('success', 'Down payment submitted successfully.');
    } catch (\Throwable $th) {
      return redirect()
        ->route('downpayments.index')
        ->with('error', $th->getMessage());
    }
  }

  public function disburseBike()
  {
    $this->dispatch('close-modal');

    $disbursement_account = Account::where('partner_id', $this->loanApplication->Partner_ID)
      ->where('slug', AccountSeederService::DISBURSEMENT_OVA_SLUG)
      ->first();

    if ($disbursement_account->balance < $this->loanApplication->Amount) {
      return redirect()->route('downpayments.show', $this->loanApplication)
        ->with('error', 'Insufficient balance in the disbursement account');
    }
    // Create loan
    $response = AssetLoans::affectDisbursementAccounts($this->loanApplication);

    $response = json_decode($response->content(), true);

    if (Arr::get($response, 'returnCode') === 200) {
      $loan = Loan::query()->firstWhere('Loan_Application_ID', $this->loanApplication->id);
      $this->loanApplication->customerAsset()->update([
        'Status' => 'Disbursed',
        'Disbursed_At' => now()->toDateTimeString(),
        'Disbursed_By' => auth()->id(),
        'Loan_ID' => $loan?->id,
      ]);
      $this->loanApplication->downPayment()->update([
        'Asset_Disbursement_Status' => 'Disbursed',
      ]);

      event(new LoanDisbursed($loan));

      return redirect()->route('downpayments.index')->with('success', 'Loan created successfully.');
    }

    return redirect()->route('downpayments.show', $this->loanApplication)
      ->with('error', 'There was a problem creating the loan. Please check with the Administrator.');
  }

  public function removeFile($file)
  {
    Storage::delete($file);

    return redirect()
      ->route('downpayments.show', $this->loanApplication)
      ->with('success', 'File deleted successfully.');
  }

  protected function saveValidationDetails(array $details): array
  {
    if (empty($details)) {
      return [];
    }

    return Validation::query()->firstOrCreate([
      'loan_application_id' => $this->loanApplication->id,
      'customer_id' => $this->loanApplication->Customer_ID,
    ], $details)->only(['name', 'date_of_birth', 'nin', 'nin_status', 'timestamp', 'status']);
  }

    /**
     * @return mixed[]
     */
    protected function getUploadedFiles(): array
    {
        $customerFiles = $this->customerFiles ?? Storage::allFiles('customer/loan-applications/' . $this->loanApplication->id);

        return collect($customerFiles)
            ->map(function ($file) {
                return str($file)
                    ->after($this->loanApplication->id . '/')
                    ->beforeLast('/')
                    ->toString();
            })->toArray();
    }

    protected function hasUploadedDisbursementDocuments(): bool
    {
        return ! $this->loanApplication->Disbursement_Documents_Uploaded && collect(array_flip($this->getUploadedFiles()))->has(['caveated_logbook', 'driving_permit']);
    }
}
