<?php

namespace App\Livewire\Reports;

use App\Actions\Reports\GetDisbursementReportDetailsAction;
use App\Actions\Reports\GetRepaymentReportDetailsAction;
use App\Exports\DisbursementExport;
use App\Services\PdfGeneratorService;
use App\Traits\ExportsData;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Carbon;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class DisbursementReport extends Component
{
    use ExportsData, WithPagination;

    public function mount()
    {
        $this->startDate = now()->startOfMonth()->format('Y-m-d');
        $this->endDate = now()->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.reports.disbursement-report', [
            'records' => $this->getReportData()
        ]);
    }

    public function printReport()
    {
        $viewData = [
            'records' => app(GetDisbursementReportDetailsAction::class)
                ->filters($this->getFilters())
                ->execute(),
            'partnerName' => auth()->user()?->partner->Institution_Name,
            'filters' => $this->getFormattedDateFilters()
        ];

        return app(PdfGeneratorService::class)
            ->view('pdf.loan-disbursements', $viewData)
            ->streamFromLivewire();
    }

    public function excelExport(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        return Excel::download(new DisbursementExport($this->getFilters()), $this->getFilename());
    }

    public function exportCBA()
    {
        $viewData = [
            'records' => app(GetDisbursementReportDetailsAction::class)
                ->filters($this->getFilters())
                ->execute(),
            'partnerName' => auth()->user()?->partner->Institution_Name,
            'filters' => $this->getFormattedDateFilters()
        ];

        return '';
    }

    private function getReportData()
    {
        if (empty($this->endDate) || empty($this->startDate)) {
            return collect();
        }

        return app(GetDisbursementReportDetailsAction::class)
            ->paginate()
            ->filters($this->getFilters())
            ->execute();
    }

    /**
     * @return array
     */
    public function getFilters(): array
    {
        return [
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
        ];
    }

    /**
     * @return string
     */
    public function getFilename(): string
    {
        return str(self::class)->afterLast('\\')->snake()->toString() . now()->toDateString() . '.xlsx';
    }
}
