<?php

namespace App\Http\Controllers;

use App\Models\Scopes\PartnerScope;
use Carbon\Carbon;
use App\Models\Loan;
use App\Models\LoanFee;
use App\Models\Customer;
use App\Models\LoanPenalty;
use App\Models\JournalEntry;
use App\Models\LoanSchedule;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Http\Request;
use App\Models\LoanRepayment;
use App\Models\LoanProductFee;
use App\Models\LoanApplication;
use App\Models\LoanDisbursement;
use App\Models\WrittenOffLoan;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Illuminate\View\View;

class LoanReportsController extends Controller
{
    public function disbursement()
    {
        return view('reports.loans.disbursement');
    }

    public function exportToPDF(Request $request)
    {
        // Get the data from the request
        $data = $request->input('data');

        // Load a view with the data
        $pdf = Pdf::loadView('pdf.export', compact('data'));

        // Download the PDF file
        return $pdf->download('export_' . date('YmdHis') . '.pdf');
    }

    public function exportToCSV(Request $request)
    {
        // Get the data from the request
        $data = $request->input('data');

        // Create a temporary file
        $filename = "export_" . date('YmdHis') . ".csv";
        $handle = fopen($filename, 'w+');

        // Write data to the file
        foreach ($data as $row) {
            fputcsv($handle, $row);
        }

        fclose($handle);

        // Set headers for download
        $headers = [
            'Content-Type' => 'text/csv',
        ];

        // Return the file as a download response
        return Response::download($filename, $filename, $headers)->deleteFileAfterSend(true);
    }

    // public function collections()
    // {
    //     $collections = JournalEntry::where('transactable', LoanRepayment::class)
    //         ->where('credit_amount', '>', 0)
    //         ->where('partner_id', Auth::user()->partner_id)
    //         ->with('customer', 'account')
    //         ->get();

    //     return view('reports.loans.collections', compact('collections'));
    // }
    public function collections()
    {
        $partnerId = Auth::user()->partner_id;

        if (empty($partnerId) && Auth::user()->is_admin) {
            // Query from the loan_repayments table
            $collections = LoanRepayment::query()
                ->with([
                    'loan',
                    'customer',
                    'partner'
                ])
                ->get();
        } else {

            $collections = LoanRepayment::query()
                ->with([
                    'loan',
                    'customer',
                    'partner'
                ])
                ->where('Partner_ID', Auth::user()->partner_id)
                ->get();
        }

        return view('reports.loans.collections', compact('collections'));
    }

    public function outstanding()
    {
        return view('reports.loans.outstanding');
    }

    public function paidOff(): View
    {
        return view('reports.loans.paid_off');
    }

    public function repaymentReport(): View
    {
        return view('reports.loans.repayment_report');
    }

    public function provisionsReport(): View
    {
        return view('reports.loans.provisions_report');
    }

    public function writtenOffReport(): View
    {
        return view('reports.loans.written_off_report');
    }

    public function interestReceivable(): View
    {
        return view('reports.loans.interest_receivable');
    }

    public function writtenOffRecoveredReport(): View
    {
        return view('reports.loans.written_off_recovered_report');
    }

    public function overdue(): View
    {
        return view('reports.loans.overdue');
    }

    public function portfolio_at_risk(): View
    {
        return view('reports.loans.portfolio_at_risk');
    }

    public function arrearsAgingReport()
    {
        return view('reports.loans.loan-ageing-report');
    }

    public function monthlyReport(Request $request)
    {
        $currentDate = Carbon::now();
        $months = [];

        for ($i = 0; $i < 6; $i++) {
            $month = $currentDate->subMonths($i);
            $months[] = $month->format('Y-m');
        }

        $reportData = [];

        foreach ($months as $month) {
            $full_month = $month . '-01';
            $startOfMonth = Carbon::parse($full_month)->startOfMonth();
            $endOfMonth = Carbon::parse($full_month)->endOfMonth();

            // Principal Balance
            $principalBalance = Loan::whereBetween('Credit_Account_Date', [$startOfMonth, $endOfMonth])
                ->sum('Credit_Amount');

            // Principal Received
            $principalReceived = LoanRepayment::whereBetween('Transaction_Date', [$startOfMonth, $endOfMonth])
                ->sum('amount');

            // Interest Received
            $interestReceived = LoanRepayment::whereBetween('Transaction_Date', [$startOfMonth, $endOfMonth])
                ->sum('amount') - LoanRepayment::whereBetween('Transaction_Date', [$startOfMonth, $endOfMonth])
                ->sum('Last_Payment_Amount'); // Adjust based on actual interest calculation

            // Fees Received
            $feesReceived = LoanFee::whereBetween('created_at', [$startOfMonth, $endOfMonth])
                ->sum('Amount');

            // Penalty Received
            $penaltyReceived = LoanPenalty::whereBetween('created_at', [$startOfMonth, $endOfMonth])
                ->sum('Amount');

            // Total Received
            $totalReceived = $principalReceived + $interestReceived + $feesReceived + $penaltyReceived;

            // New Loans
            $newLoans = Loan::whereBetween('created_at', [$startOfMonth, $endOfMonth])->count();

            // Number of Repayments
            $numberOfRepayments = LoanRepayment::whereBetween('Transaction_Date', [$startOfMonth, $endOfMonth])->count();

            // Pending Due
            $pendingDue = Loan::whereBetween('Maturity_Date', [$startOfMonth, $endOfMonth])
                ->where('Credit_Account_Status', '!=', 'Fully Paid')
                ->sum('Credit_Amount');

            // Number of Fully Paid Loans
            $fullyPaidLoans = Loan::whereBetween('Credit_Account_Closure_Date', [$startOfMonth, $endOfMonth])
                ->where('Credit_Account_Status', 'Fully Paid')
                ->count();

            $reportData[$month] = [
                'principal_balance' => number_format($principalBalance, 2),
                'principal_received' => number_format($principalReceived, 2),
                'interest_received' => number_format($interestReceived, 2),
                'fees_received' => number_format($feesReceived, 2),
                'penalty_received' => number_format($penaltyReceived, 2),
                'total_received' => number_format($totalReceived, 2),
                'new_loans' => $newLoans,
                'number_of_repayments' => $numberOfRepayments,
                'pending_due' => number_format($pendingDue, 2),
                'number_of_fully_paid_loans' => $fullyPaidLoans,
            ];
        }

        return view('reports.loans.monthly_loan_summary_monthly_report', compact('reportData', 'months'));
    }

    public function pendingDisbursements()
    {
        return view('reports.loans.pending_disbursement_report');
    }

    public function rejectedApplications()
    {

        $rejected_applications = LoanApplication::with([
            "customer",
            "loan_product"
        ])
            ->where('Credit_Application_Status', 'Rejected')
            ->orderBy('id', 'desc')
            ->paginate(100);

        return view('reports.loans.rejected_applications_report', compact('rejected_applications'));
    }

    public function loansInArrearsReport(Request $request)
    {
        $loans = Loan::select(
            'c.First_Name AS Name',
            'c.Telephone_Number AS Telephone_Number',
            'loans.id AS LoanID',
            'ls.principal AS Principal',
            'ls.total_payment AS Due',
            DB::raw('COALESCE(SUM(lr.amount), 0) AS Paid'),
            DB::raw('COALESCE(SUM(CASE WHEN ls.payment_due_date < CURRENT_DATE THEN ls.total_payment ELSE 0 END) - SUM(lr.amount), 0) AS PastDue'),
            DB::raw('COALESCE(SUM(CASE WHEN ls.payment_due_date = CURRENT_DATE THEN ls.total_payment ELSE 0 END), 0) AS PendingDue'),
            'loans.Credit_Account_Status'
        )
            ->join('customers as c', 'loans.Customer_ID', '=', 'c.id')
            ->leftJoin('loan_schedules as ls', 'loans.id', '=', 'ls.loan_id')
            ->leftJoin('loan_repayments as lr', 'loans.id', '=', 'lr.Loan_ID')
            ->where('ls.payment_due_date', '<', DB::raw('CURRENT_DATE')) // Overdue condition
            ->whereRaw('ls.payment_due_date = (SELECT MAX(payment_due_date) FROM loan_schedules WHERE loan_id = loans.id)') // Last collection date
            ->whereRaw('lr.amount IS NOT NULL AND lr.amount > 0') // Part-payment received
            ->where('loans.Maturity_Date', '>', DB::raw('CURRENT_DATE')) // Loan not expired
            ->groupBy('c.First_Name', 'c.Telephone_Number', 'loans.id', 'ls.principal', 'ls.total_payment', 'loans.Credit_Account_Status', 'ls.payment_due_date')
            ->orderBy('loans.id', 'desc')
            ->paginate(100);

        return view('reports.loans.arrears_report', compact('loans'));
    }

    public function dueLoansReport(Request $request)
    {
        return view('reports.loans.due_loans_report');
    }


    public function missedRepayments(Request $request)
    {
        $loans = Loan::select(
            'c.First_Name AS Name',
            'loans.id AS LoanID',
            'ls.principal AS Principal',
            'ls.total_payment AS Due',
            DB::raw('COALESCE(SUM(lr.amount), 0) AS Paid'),
            DB::raw('COALESCE(SUM(CASE WHEN ls.payment_due_date < CURRENT_DATE THEN ls.total_payment ELSE 0 END) - SUM(lr.amount), 0) AS PastDue'),
            'loans.Credit_Amortization_Type AS Amortization',
            DB::raw('COALESCE(SUM(CASE WHEN ls.payment_due_date = CURRENT_DATE THEN ls.total_payment ELSE 0 END), 0) AS PendingDue'),
            DB::raw('DATEDIFF(CURRENT_DATE, ls.payment_due_date) AS DaysPast'),
            DB::raw('MAX(lr.Transaction_Date) AS LastPayment'),
            'loans.Credit_Account_Status'
        )
            ->join('customers as c', 'loans.Customer_ID', '=', 'c.id')
            ->leftJoin('loan_schedules as ls', 'loans.id', '=', 'ls.loan_id')
            ->leftJoin('loan_repayments as lr', 'loans.id', '=', 'lr.Loan_ID')
            ->where('ls.payment_due_date', '<', DB::raw('CURRENT_DATE')) // Overdue condition
            ->whereRaw('ls.payment_due_date = (SELECT MAX(payment_due_date) FROM loan_schedules WHERE loan_id = loans.id)') // Last collection date
            ->whereRaw('lr.amount IS NULL OR lr.amount = 0') // No payment for the last collection date
            ->groupBy('c.First_Name', 'loans.id', 'ls.principal', 'ls.total_payment', 'loans.Credit_Amortization_Type', 'loans.Credit_Account_Status', 'ls.payment_due_date')
            ->orderBy('loans.id', 'desc')
            ->paginate(100);

        return view('reports.loans.missed-repayments-report', compact('loans'));
    }
}
