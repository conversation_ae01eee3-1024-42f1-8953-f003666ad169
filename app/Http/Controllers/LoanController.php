<?php

namespace App\Http\Controllers;

use App\Actions\Loans\WriteOffLoanAction;
use App\Enums\LoanAccountType;
use App\Models\Loan;
use Illuminate\Http\Request;
use App\Models\LoanApplication;
use App\Models\LoanDisbursement;
use App\Models\LoanSchedule;
use App\Models\LoanRepayment;
use App\Models\WrittenOffLoan;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class LoanController extends Controller
{
    public function index()
    {
        return view('loans.index');
    }

    public function writeOff(Request $request, WriteOffLoanAction $action, Loan $loan): \Illuminate\Http\RedirectResponse
    {
        $details = $request->validate([
            'reason' => ['required', 'string', 'max:255'],
            'write_off_date' => ['required', 'date', 'before_or_equal:today'],
        ]);

        try {
            return redirect()
                ->route('loan-accounts.show', ['loan' => $action->execute($loan, $details)])
                ->with('success', 'Loan written off successfully.');
        } catch (\Throwable $th) {
            return back()->withError($th->getMessage());
        }
    }

    public function store(Request $request, LoanApplication $loanApplication)
    {
        $loanApplication->load('loan_product');

        try {
            $loan = new Loan();
            $loan->fill([
                'Partner_ID' => $loanApplication->Partner_ID,
                'Customer_ID' => $loanApplication->Customer_ID,
                'Loan_Product_ID' => $loanApplication->Loan_Product_ID,
                'Loan_Application_ID' => $loanApplication->id,
                'Credit_Account_Reference' => Loan::generateReference(),
                'Credit_Account_Date' => $loanApplication->Credit_Application_Date,
                'Credit_Amount' => $loanApplication->Amount,
                'Facility_Amount_Granted' => $loanApplication->Amount,
                'Credit_Amount_Drawdown' => '0.00',
                'Credit_Account_Type' => 'uwow',
                'Currency' => 'UGX',
                'Maturity_Date' => $loanApplication->Credit_Application_Date,
                'Annual_Interest_Rate_at_Disbursement' => 0,
                'Date_of_First_Payment' => $loanApplication->Credit_Application_Date,
                'Credit_Amortization_Type' => 1, // Refer to the DSM APPENDIX 1.11
                'Credit_Payment_Frequency' => "Monthly",
                'Number_of_Payments' => 1,
                'Term' => 1,
                'Type_of_Interest' => 1, // Refer to the DSM APPENDIX 1.9 0-Fixed, 1-Floating
                'Interest_Rate' => 2,
                'Interest_Calculation_Method' => 'Flat',
                'Loan_Term_ID' => 2,
            ])->save();
            session()->flash("success", "Loan approved successfully");
        } catch (\Throwable $th) {
            session()->flash("error", $th->getMessage());
        }
        //return redirect()->route('loan-applications.index');
        return redirect()->back();
    }

    public function show(Request $request, Loan $loan)
    {
        $loan->load(['loan_product', 'customer', 'loan_application']);

        return view('loans.show', compact('loan'));
    }

    public function update(Request $request, Loan $loan) {}

    public function disburse(Loan $loan) {
        //$this->createTrackingSession($loanApplication);

        LoanDisbursement::createDisbursement($loan);
        LoanSchedule::generateSchedule($loan);

        return true;
    }

    public function makePayment(Request $request, $loan)
    {
        return view('loan-applications.loan-repayment', compact('loan'));
    }

    public function repayment(Request $request, Loan $loan) {

        try {
            $repayment = new LoanRepayment();
            $repayment->createPayment($loan, $request->amount);
            $repayment->affectAccounts();
            session()->flash("success", "Payment made successfully");
        } catch (\Throwable $th) {
            //session()->flash("error", "Loan Payment Failed");
            logger($th->getMessage());

            return back()->with('error', 'Something went wrong');
        }

        return back();
    }


    public function noRepayments(Request $request)
    {
        $startDate = $request->input('start_date', null);
        $endDate = $request->input('end_date', null);
        $status = $request->input('status', null);

        $loans = Loan::select(
            'c.First_Name AS FirstName',
            'c.Last_Name AS LastName',
            'loans.id AS LoanID',
            'loans.Credit_Account_Date AS LoanStartDate',
            'loans.Credit_Amount AS Principal',
            'loans.Credit_Account_Status'
        )
            ->join('customers as c', 'loans.Customer_ID', '=', 'c.id')
            ->leftJoin('loan_repayments as lr', 'loans.id', '=', 'lr.Loan_ID')
            ->whereRaw('lr.Loan_ID IS NULL OR lr.amount IS NULL OR lr.amount = 0') // No payments received
            ->when($startDate, function ($query, $startDate) {
                return $query->where('loans.Credit_Account_Date', '>=', $startDate);
            })
            ->when($endDate, function ($query, $endDate) {
                return $query->where('loans.Credit_Account_Date', '<=', $endDate);
            })
            ->when($status, function ($query, $status) {
                return $query->where('loans.Credit_Account_Status', $status);
            })
            ->groupBy('c.First_Name','c.Last_Name', 'loans.id', 'loans.Credit_Account_Date', 'loans.Credit_Amount', 'loans.Credit_Account_Status')
            ->orderBy('loans.id', 'desc')
            ->paginate(100);

        $statuses = [
            Loan::ACCOUNT_STATUS_FULLY_PAID_OFF => "Fully Paid",
            Loan::ACCOUNT_STATUS_WRITTEN_OFF => "Written-off",
            Loan::ACCOUNT_STATUS_CURRENT_AND_WITHIN_TERMS => "Within Terms",
            Loan::ACCOUNT_STATUS_OUTSTANDING_AND_BEYOND_TERMS => "Outstanding and Beyond Terms",
        ];

        return view('loans.no-repayments', compact('loans', 'statuses', 'startDate', 'endDate', 'status'));
    }

    public function pastMaturityDate(Request $request)
    {
        $loans = Loan::select(
            'c.First_Name AS FirstName',
            'c.Last_Name AS LastName',
            'loans.id AS LoanID',
            'loans.Credit_Amount AS Principal',
            DB::raw('COALESCE(SUM(ls.total_payment), 0) AS Due'),
            DB::raw('COALESCE(SUM(lr.amount), 0) AS Paid'),
            DB::raw('COALESCE(loans.Credit_Amount - SUM(lr.amount), loans.Credit_Amount) AS Balance'),
            'loans.Maturity_Date AS Maturity',
            DB::raw('DATEDIFF(CURRENT_DATE, loans.Maturity_Date) AS DaysPast'),
            DB::raw('MAX(lr.Transaction_Date) AS LastPayment'),
            'loans.Credit_Account_Status'
        )
            ->join('customers as c', 'loans.Customer_ID', '=', 'c.id')
            ->leftJoin('loan_schedules as ls', 'loans.id', '=', 'ls.loan_id')
            ->leftJoin('loan_repayments as lr', 'loans.id', '=', 'lr.Loan_ID')
            ->where('loans.Maturity_Date', '<', DB::raw('CURRENT_DATE')) // Past maturity date
            ->groupBy('c.First_Name', 'c.Last_Name', 'loans.id', 'loans.Credit_Amount', 'loans.Maturity_Date', 'loans.Credit_Account_Status')
            ->havingRaw('COALESCE(loans.Credit_Amount - SUM(lr.amount), loans.Credit_Amount) > 0') // Not fully paid
            ->orderBy('loans.id', 'desc')
            ->paginate(100);

        return view('loans.past-maturity-date', compact('loans'));
    }

    public function principalOutstanding(Request $request)
    {
        $loans = Loan::select(
            'c.First_Name AS FirstName',
            'c.Last_Name AS LastName',
            'loans.id AS LoanID',
            'loans.Credit_Account_Date AS Released',
            'loans.Maturity_Date AS Maturity',
            'loans.Credit_Amount AS Principal',
            'lr.PrincipalPaid',
            DB::raw('COALESCE(loans.Credit_Amount - lr.PrincipalPaid, loans.Credit_Amount) AS PrincipalBalance'),
            DB::raw('COALESCE(lr.PrincipalPaidTillToday, 0) AS PrincipalPaidTillToday'),
            DB::raw('COALESCE(loans.Credit_Amount - lr.PrincipalPaidTillToday, loans.Credit_Amount) AS PrincipalBalanceTillToday'),
            'loans.Credit_Account_Status'
        )
            ->join('customers as c', 'loans.Customer_ID', '=', 'c.id')
            ->leftJoin(DB::raw("
        (SELECT
            Loan_ID,
            COALESCE(SUM(amount), 0) AS PrincipalPaid,
            COALESCE(SUM(amount), 0) AS PrincipalPaidTillToday
            -- COALESCE(SUM(CASE WHEN Transaction_Date <= CURRENT_DATE THEN amount ELSE 0 END), 0) AS PrincipalPaidTillToday
         FROM loan_repayments
         GROUP BY Loan_ID) AS lr
    "), 'loans.id', '=', 'lr.Loan_ID')
            ->leftJoin('loan_schedules as ls', 'loans.id', '=', 'ls.loan_id')
            ->whereIn('loans.Credit_Account_Status', [
                Loan::ACCOUNT_STATUS_CURRENT_AND_WITHIN_TERMS,
                Loan::ACCOUNT_STATUS_OUTSTANDING_AND_BEYOND_TERMS
            ])
            // ->whereRaw('ls.payment_due_date <= CURRENT_DATE') // Ensure due dates are till today
            ->groupBy('c.First_Name', 'c.Last_Name', 'loans.id', 'loans.Credit_Account_Date', 'loans.Maturity_Date', 'loans.Credit_Amount', 'loans.Credit_Account_Status', 'lr.PrincipalPaid', 'lr.PrincipalPaidTillToday')
            ->orderBy('loans.id', 'desc')
            ->paginate(100);

        // Debugging

        return view('loans.principal-outstanding', compact('loans'));
    }

    public function oneMonthLateLoans(Request $request)
    {
        // TODO these statuses seems necessary
        // Loan::ACCOUNT_STATUS_ARREARS
        // Loan::ACCOUNT_STATUS_PAST_MATURITY
        $loans = Loan::select(
            'loans.Credit_Account_Date AS DisbursementDate',
            'c.First_Name AS FirstName',
            'c.Last_Name AS LastName',
            'loans.id AS LoanID',
            'loans.Credit_Amount AS Principal',
            DB::raw('COALESCE(SUM(ls.total_payment), 0) AS Due'),
            DB::raw('COALESCE(SUM(lr.amount), 0) AS Paid'),
            DB::raw('COALESCE(loans.Credit_Amount - SUM(lr.amount), loans.Credit_Amount) AS Balance'),
            DB::raw('COALESCE(SUM(CASE WHEN ls.payment_due_date = CURRENT_DATE THEN ls.total_payment ELSE 0 END), 0) AS PendingDue'),
            DB::raw('MAX(lr.Transaction_Date) AS LastPayment'),
            'loans.Credit_Account_Status'
        )
            ->join('customers as c', 'loans.Customer_ID', '=', 'c.id')
            ->leftJoin('loan_schedules as ls', 'loans.id', '=', 'ls.loan_id')
            ->leftJoin('loan_repayments as lr', 'loans.id', '=', 'lr.Loan_ID')
            ->whereRaw('
                (loans.Credit_Account_Status = "' . Loan::ACCOUNT_STATUS_OUTSTANDING_AND_BEYOND_TERMS . '")
                AND DATEDIFF(CURRENT_DATE, (SELECT MAX(payment_due_date) FROM loan_schedules WHERE loan_id = loans.id AND principal_remaining = 0)) >= 30
            ')
            ->groupBy('c.First_Name', 'c.Last_Name', 'loans.id', 'loans.Credit_Account_Date', 'loans.Credit_Amount', 'loans.Credit_Account_Status')
            ->orderBy('loans.id', 'desc')
            ->paginate(100);

        return view('loans.one-month-late', compact('loans'));
    }

    public function threeMonthsLateLoans(Request $request)
    {
        // TODO these statuses seems necessary
        // Loan::ACCOUNT_STATUS_ARREARS
        // Loan::ACCOUNT_STATUS_PAST_MATURITY
        $loans = Loan::select(
            'loans.Credit_Account_Date AS DisbursementDate',
            'c.First_Name AS FirstName',
            'c.Last_Name AS LastName',
            'loans.id AS LoanID',
            'loans.Credit_Amount AS Principal',
            DB::raw('COALESCE(SUM(ls.total_payment), 0) AS Due'),
            DB::raw('COALESCE(SUM(lr.amount), 0) AS Paid'),
            DB::raw('COALESCE(loans.Credit_Amount - SUM(lr.amount), loans.Credit_Amount) AS Balance'),
            DB::raw('COALESCE(SUM(CASE WHEN ls.payment_due_date = CURRENT_DATE THEN ls.total_payment ELSE 0 END), 0) AS PendingDue'),
            DB::raw('MAX(lr.Transaction_Date) AS LastPayment'),
            'loans.Credit_Account_Status'
        )
            ->join('customers as c', 'loans.Customer_ID', '=', 'c.id')
            ->leftJoin('loan_schedules as ls', 'loans.id', '=', 'ls.loan_id')
            ->leftJoin('loan_repayments as lr', 'loans.id', '=', 'lr.Loan_ID')
            ->whereRaw('
                (loans.Credit_Account_Status = "' . Loan::ACCOUNT_STATUS_OUTSTANDING_AND_BEYOND_TERMS . '")
                AND DATEDIFF(CURRENT_DATE, (SELECT MAX(payment_due_date) FROM loan_schedules WHERE loan_id = loans.id AND principal_remaining = 0)) >= 90
            ')
            ->groupBy('c.First_Name', 'c.Last_Name', 'loans.id', 'loans.Credit_Account_Date', 'loans.Credit_Amount', 'loans.Credit_Account_Status')
            ->orderBy('loans.id', 'desc')
            ->paginate(100);

        return view('loans.three-month-late', compact('loans'));
    }
}
