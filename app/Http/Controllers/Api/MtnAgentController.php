<?php

namespace App\Http\Controllers\Api;

use App\Actions\RegisterCustomerAction;
use App\Http\Controllers\Controller;
use App\Jobs\CompleteCustomerRegistration;
use App\Models\Customer;
use App\Services\MtnApiService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Requests\DisbursementRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class MtnAgentController extends Controller
{
    public function __construct(
        private readonly MtnApiService $mtnService
    ) {}

    public function disburse(DisbursementRequest $request): JsonResponse
    {
        try {
            $result = $this->mtnService->disburse(
                $request->msisdn,
                $request->amount,
                $request->reference
            );

            return response()->json([
                'status' => 'success',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function customerDetails(Request $request): string
    {
        $details = json_decode(json_encode(simplexml_load_string($request->getContent())), true);

        $stringWithPhone = Arr::get($details, 'resource');
        $phoneNumber = str($stringWithPhone)->after('FRI:')->before('/')->toString();

        $customer = Customer::query()->firstWhere('Telephone_Number', $phoneNumber);

        if (! $customer || $customer->doesNotHaveOptionalSavingsAccount()) {
            // Create the XML structure as a string
            return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<ns0:getcustomerdetailsresponse xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">' . PHP_EOL .
                '   <status>UNREGISTERED</status>' . PHP_EOL .
                '   <savingsaccounts/>' . PHP_EOL .
                '   <loanaccounts/>' . PHP_EOL .
                '</ns0:getcustomerdetailsresponse>';
        }

        if ($customer->loans->isEmpty()) {
            return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<p:getcustomerdetailsresponse xmlns:p="http://www.ericsson.com/em/emm/sp/backend" '
                . 'xmlns:iso="urn:iso:std:iso:20022:tech:xsd" '
                . 'xmlns:op="http://www.ericsson.com/em/emm/common" '
                . 'xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" '
                . 'xsi:schemaLocation="http://www.ericsson.com/em/emm/sp/backend savingandlendingbackend.xsd'
                . '">' . PHP_EOL .
                '   <status>REGISTERED</status>' . PHP_EOL .
                '   <savingsaccounts>' . PHP_EOL .
                '      <savingsaccount>' . PHP_EOL .
                '         <accountnumber>' . data_get($customer->options, 'savingsaccount.accountnumber') . '</accountnumber>' . PHP_EOL .
                '         <status>' . data_get($customer->options, 'savingsaccount.status') . '</status>' . PHP_EOL .
                '         <balance>' . PHP_EOL .
                '            <amount>' . data_get($customer->options, 'savingsaccount.balance.amount') . '</amount>' . PHP_EOL .
                '            <currency>' . data_get($customer->options, 'savingsaccount.balance.currency') . '</currency>' . PHP_EOL .
                '         </balance>' . PHP_EOL .
                '         <savingsaccounttype>' . data_get($customer->options, 'savingsaccount.savingsaccounttype') . '</savingsaccounttype>' . PHP_EOL .
                '      </savingsaccount>' . PHP_EOL .
                '   </savingsaccounts>' . PHP_EOL .
                '   <loanaccounts/>' . PHP_EOL .
                '</p:getcustomerdetailsresponse>';
        }

        // todo: Add loan account to the details
        return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<p:getcustomerdetailsresponse xmlns:p="http://www.ericsson.com/em/emm/sp/backend" '
            . 'xmlns:iso="urn:iso:std:iso:20022:tech:xsd" '
            . 'xmlns:op="http://www.ericsson.com/em/emm/common" '
            . 'xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" '
            . 'xsi:schemaLocation="http://www.ericsson.com/em/emm/sp/backend savingandlendingbackend.xsd'
            . '">' . PHP_EOL .
            '   <status>REGISTERED</status>' . PHP_EOL .
            '   <savingsaccounts>' . PHP_EOL .
            '      <savingsaccount>' . PHP_EOL .
            '         <accountnumber>' . data_get($customer->options, 'savingsaccount.accountnumber') . '</accountnumber>' . PHP_EOL .
            '         <status>' . data_get($customer->options, 'savingsaccount.status') . '</status>' . PHP_EOL .
            '         <balance>' . PHP_EOL .
            '            <amount>' . data_get($customer->options, 'savingsaccount.balance.amount') . '</amount>' . PHP_EOL .
            '            <currency>' . data_get($customer->options, 'savingsaccount.balance.currency') . '</currency>' . PHP_EOL .
            '         </balance>' . PHP_EOL .
            '         <savingsaccounttype>' . data_get($customer->options, 'savingsaccount.savingsaccounttype') . '</savingsaccounttype>' . PHP_EOL .
            '      </savingsaccount>' . PHP_EOL .
            '   </savingsaccounts>' . PHP_EOL .
            '   <loanaccounts/>' . PHP_EOL .
            '</p:getcustomerdetailsresponse>';
    }

    public function registerCustomer(Request $request, RegisterCustomerAction $registerAction): string
    {
        $details = json_decode(json_encode(simplexml_load_string($request->getContent())), true);

        try {
            $customer = $registerAction->useMtnKyc()->execute($details);

            $savingAccountNumber = '1';

            if (str($customer->id)->length() < 10) {
                $savingAccountNumber .= str($customer->id)->padLeft(10, '0');
            } else {
                $savingAccountNumber = $customer->id;
            }

            $options = $customer->options;
            $options['savingsaccount'] = [
                'accountnumber' => $savingAccountNumber,
                'status' => 'ACTIVE',
                'balance' => [
                    'amount' => 0,
                    'currency' => 'UGX'
                ],
                'savingsaccounttype' => 'SAVINGS'
            ];
            $customer->options = $options;
            $customer->save();


            dispatch(new CompleteCustomerRegistration($customer));

            return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL
                . '<p:customerregistrationresponse xmlns:p="http://www.ericsson.com/em/emm/sp/backend" '
                . 'xmlns:iso="urn:iso:std:iso:20022:tech:xsd" '
                . 'xmlns:op="http://www.ericsson.com/em/emm/common" '
                . 'xmlns:soap-env="http://schemas.xmlsoap.org/soap/envelope/">' . PHP_EOL
                . '    <status>PENDING</status>' . PHP_EOL
                . '    <message>Your request to register for Weekend Loan services has been received, please wait for a confirmation shortly</message>' . PHP_EOL
                . '</p:customerregistrationresponse>';
        } catch (\Exception $e) {
            Log::error('MTN Register Customer Error: ' . $e->getMessage());

            $xml = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<ns0:customerregistrationresponse xmlns:ns0="http://www.ericsson.com/em/emm/sp/frontend">' . PHP_EOL .
                '   <errorcode>ACCOUNT_NOT_CREATED</errorcode>' . PHP_EOL .
                '</ns0:customerregistrationresponse>';

            return response($xml, 500)
                ->header('Content-Type', 'application/xml');
        }
    }

    public function completeCustomerRegistration(Request $request): string
    {
        $details = json_decode(json_encode(simplexml_load_string($request->getContent())), true);

        $resource = Arr::get($details, 'resource');
        $phoneNumber = str($resource)->after('FRI:')->before('@')->toString();
        $customer = Customer::query()->firstWhere('Telephone_Number', $phoneNumber);

        if (! $customer) {
            Log::debug('MTN Completing Customer Registration Error:' . $phoneNumber . ' from ' . $resource);

            return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<ns0:customerregistrationcompletedresponse xmlns:ns0="http://www.ericsson.com/em/emm/sp/frontend">' . PHP_EOL .
                '   <errorcode>ACCOUNT_NOT_FOUND</errorcode>' . PHP_EOL .
                '</ns0:customerregistrationcompletedresponse>';
        }

        $options = $customer->options;
        $options['savingsaccount'] = Arr::get($details, 'savingsaccount');
        $customer->options = $options;
        $customer->save();


        // Customer has successfully registered with us.
        return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<ns0:customerregistrationcompletedresponse xmlns:ns0="http://www.ericsson.com/em/emm/sp/frontend"/>';
    }

    public function completeLoanApplication(Request $request): string
    {
        $details = json_decode(json_encode(simplexml_load_string($request->getContent())), true);

        return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<ns0:loanapplicationcompletedresponse xmlns:ns0="http://www.ericsson.com/em/emm/sp/frontend"/>';
    }

    public function getTransactionStatus(string $referenceId): JsonResponse
    {
        try {
            $result = $this->mtnService->getTransactionStatus($referenceId);

            return response()->json([
                'status' => 'success',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
