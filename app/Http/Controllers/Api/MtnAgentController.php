<?php

namespace App\Http\Controllers\Api;

use App\Actions\Loans\ProcessLoanRepaymentAction;
use App\Actions\RegisterCustomerAction;
use App\Http\Controllers\Controller;
use App\Jobs\CompleteCustomerRegistration;
use App\Models\Customer;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class MtnAgentController extends Controller
{
    public function customerDetails(Request $request): string
    {
        $details = json_decode(json_encode(simplexml_load_string($request->getContent())), true);

        $stringWithPhone = Arr::get($details, 'resource');
        $phoneNumber = str($stringWithPhone)->after('FRI:')->before('/')->toString();

        $customer = Customer::query()->firstWhere('Telephone_Number', $phoneNumber);

        if (! $customer || $customer->doesNotHaveOptionalSavingsAccount()) {
            // Create the XML structure as a string
            return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<ns0:getcustomerdetailsresponse xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">' . PHP_EOL .
                '   <status>UNREGISTERED</status>' . PHP_EOL .
                '   <savingsaccounts/>' . PHP_EOL .
                '   <loanaccounts/>' . PHP_EOL .
                '</ns0:getcustomerdetailsresponse>';
        }

        if ($customer->activeLoans->isEmpty()) {
            return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<p:getcustomerdetailsresponse xmlns:p="http://www.ericsson.com/em/emm/sp/backend" '
                . 'xmlns:iso="urn:iso:std:iso:20022:tech:xsd" '
                . 'xmlns:op="http://www.ericsson.com/em/emm/common" '
                . 'xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" '
                . 'xsi:schemaLocation="http://www.ericsson.com/em/emm/sp/backend savingandlendingbackend.xsd'
                . '">' . PHP_EOL .
                '   <status>REGISTERED</status>' . PHP_EOL .
                '   <savingsaccounts>' . PHP_EOL .
                '      <savingsaccount>' . PHP_EOL .
                '         <accountnumber>' . data_get($customer->options, 'savingsaccount.accountnumber') . '</accountnumber>' . PHP_EOL .
                '         <status>' . data_get($customer->options, 'savingsaccount.status') . '</status>' . PHP_EOL .
                '         <balance>' . PHP_EOL .
                '            <amount>' . data_get($customer->options, 'savingsaccount.balance.amount') . '</amount>' . PHP_EOL .
                '            <currency>' . data_get($customer->options, 'savingsaccount.balance.currency') . '</currency>' . PHP_EOL .
                '         </balance>' . PHP_EOL .
                '         <savingsaccounttype>' . data_get($customer->options, 'savingsaccount.savingsaccounttype') . '</savingsaccounttype>' . PHP_EOL .
                '      </savingsaccount>' . PHP_EOL .
                '   </savingsaccounts>' . PHP_EOL .
                '   <loanaccounts/>' . PHP_EOL .
                '</p:getcustomerdetailsresponse>';
        }

        // todo: Add loan account to the details
        $response = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<p:getcustomerdetailsresponse xmlns:p="http://www.ericsson.com/em/emm/sp/backend" '
            . 'xmlns:iso="urn:iso:std:iso:20022:tech:xsd" '
            . 'xmlns:op="http://www.ericsson.com/em/emm/common" '
            . 'xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" '
            . 'xsi:schemaLocation="http://www.ericsson.com/em/emm/sp/backend savingandlendingbackend.xsd'
            . '">' . PHP_EOL .
            '   <status>REGISTERED</status>' . PHP_EOL .
            '   <savingsaccounts>' . PHP_EOL .
            '      <savingsaccount>' . PHP_EOL .
            '         <accountnumber>' . data_get($customer->options, 'savingsaccount.accountnumber') . '</accountnumber>' . PHP_EOL .
            '         <status>' . data_get($customer->options, 'savingsaccount.status') . '</status>' . PHP_EOL .
            '         <balance>' . PHP_EOL .
            '            <amount>' . data_get($customer->options, 'savingsaccount.balance.amount') . '</amount>' . PHP_EOL .
            '            <currency>' . data_get($customer->options, 'savingsaccount.balance.currency') . '</currency>' . PHP_EOL .
            '         </balance>' . PHP_EOL .
            '         <savingsaccounttype>' . data_get($customer->options, 'savingsaccount.savingsaccounttype') . '</savingsaccounttype>' . PHP_EOL .
            '      </savingsaccount>' . PHP_EOL .
            '   </savingsaccounts>' . PHP_EOL .
            '   <loanaccounts>' . PHP_EOL;

        foreach($customer->activeLoans as $loan) {
            $loanAccount = str($this->getLoanAccountTemplate())
                ->replace('_accountNumber', $loan->Loan_Application_ID)
                ->replace('_amount', $loan->totalOutstandingBalance())
                ->replace('_dueDate', $loan->Maturity_Date)
                ->toString();

            $response .= $loanAccount . PHP_EOL;
        }

        $response .= '   </loanaccounts>' . PHP_EOL .
            '</p:getcustomerdetailsresponse>';


        return $response;
    }

    public function registerCustomer(Request $request, RegisterCustomerAction $registerAction): string
    {
        $details = json_decode(json_encode(simplexml_load_string($request->getContent())), true);

        try {
            $customer = $registerAction->useMtnKyc()->execute($details);

            $savingAccountNumber = '1';

            if (str($customer->id)->length() < 10) {
                $savingAccountNumber .= str($customer->id)->padLeft(10, '0');
            } else {
                $savingAccountNumber = $customer->id;
            }

            $options = $customer->options;
            $options['savingsaccount'] = [
                'accountnumber' => $savingAccountNumber,
                'status' => 'ACTIVE',
                'balance' => [
                    'amount' => 0,
                    'currency' => 'UGX'
                ],
                'savingsaccounttype' => 'SAVINGS'
            ];
            $customer->options = $options;
            $customer->save();


            dispatch(new CompleteCustomerRegistration($customer));

            return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL
                . '<p:customerregistrationresponse xmlns:p="http://www.ericsson.com/em/emm/sp/backend" '
                . 'xmlns:iso="urn:iso:std:iso:20022:tech:xsd" '
                . 'xmlns:op="http://www.ericsson.com/em/emm/common" '
                . 'xmlns:soap-env="http://schemas.xmlsoap.org/soap/envelope/">' . PHP_EOL
                . '    <status>PENDING</status>' . PHP_EOL
                . '    <message>Your request to register for Weekend Loan services has been received, please wait for a confirmation shortly</message>' . PHP_EOL
                . '</p:customerregistrationresponse>';
        } catch (\Exception $e) {
            Log::error('MTN Register Customer Error: ' . $e->getMessage());

            $xml = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<ns0:customerregistrationresponse xmlns:ns0="http://www.ericsson.com/em/emm/sp/frontend">' . PHP_EOL .
                '   <errorcode>ACCOUNT_NOT_CREATED</errorcode>' . PHP_EOL .
                '</ns0:customerregistrationresponse>';

            return response($xml, 500)
                ->header('Content-Type', 'application/xml');
        }
    }

    public function completeCustomerRegistration(Request $request): string
    {
        $details = json_decode(json_encode(simplexml_load_string($request->getContent())), true);

        $resource = Arr::get($details, 'resource');
        $phoneNumber = str($resource)->after('FRI:')->before('@')->toString();
        $customer = Customer::query()->firstWhere('Telephone_Number', $phoneNumber);

        if (! $customer) {
            Log::debug('MTN Completing Customer Registration Error:' . $phoneNumber . ' from ' . $resource);

            return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<ns0:customerregistrationcompletedresponse xmlns:ns0="http://www.ericsson.com/em/emm/sp/frontend">' . PHP_EOL .
                '   <errorcode>ACCOUNT_NOT_FOUND</errorcode>' . PHP_EOL .
                '</ns0:customerregistrationcompletedresponse>';
        }

        $options = $customer->options;
        $options['savingsaccount'] = Arr::get($details, 'savingsaccount');
        $customer->options = $options;
        $customer->save();

        // Customer has successfully registered with us.
        return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<ns0:customerregistrationcompletedresponse xmlns:ns0="http://www.ericsson.com/em/emm/sp/frontend"/>';
    }

    public function collectionCompleted(Request $request): string
    {
        $details = $this->xmlToArray($request->getContent());
        $transaction = Transaction::query()->firstWhere('provider_txn_id', Arr::get($details, 'transactionid', 0));

        if (! $transaction) {
            return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<bac:bankdebitcompletedresponse xmlns:bac=”http://www.ericsson.com/em/emm/sp/backend”>' . PHP_EOL .
                '  <externaltransactionid>' . $transaction->Provider_TXN_ID . '</externaltransactionid>' . PHP_EOL .
                '  <status>FAILED</status>' . PHP_EOL .
                '</bac:bankdebitcompletedresponse>';
        }

        $stringWithPhone = Arr::get($details, 'resource');
        $phoneNumber = str($stringWithPhone)->after('FRI:')->before('/')->toString();

        $customer = Customer::query()->firstWhere('Telephone_Number', $phoneNumber);

        if (! $customer || $customer->doesNotHaveOptionalSavingsAccount()) {
            // Create the XML structure as a string
            return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<ns0:getcustomerdetailsresponse xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">' . PHP_EOL .
                '   <status>UNREGISTERED</status>' . PHP_EOL .
                '   <savingsaccounts/>' . PHP_EOL .
                '   <loanaccounts/>' . PHP_EOL .
                '</ns0:getcustomerdetailsresponse>';
        }

        if ($customer->loans->isEmpty()) {
            return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<p:getcustomerdetailsresponse xmlns:p="http://www.ericsson.com/em/emm/sp/backend" '
                . 'xmlns:iso="urn:iso:std:iso:20022:tech:xsd" '
                . 'xmlns:op="http://www.ericsson.com/em/emm/common" '
                . 'xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" '
                . 'xsi:schemaLocation="http://www.ericsson.com/em/emm/sp/backend savingandlendingbackend.xsd'
                . '">' . PHP_EOL .
                '   <status>REGISTERED</status>' . PHP_EOL .
                '   <savingsaccounts>' . PHP_EOL .
                '      <savingsaccount>' . PHP_EOL .
                '         <accountnumber>' . data_get($customer->options, 'savingsaccount.accountnumber') . '</accountnumber>' . PHP_EOL .
                '         <status>' . data_get($customer->options, 'savingsaccount.status') . '</status>' . PHP_EOL .
                '         <balance>' . PHP_EOL .
                '            <amount>' . data_get($customer->options, 'savingsaccount.balance.amount') . '</amount>' . PHP_EOL .
                '            <currency>' . data_get($customer->options, 'savingsaccount.balance.currency') . '</currency>' . PHP_EOL .
                '         </balance>' . PHP_EOL .
                '         <savingsaccounttype>' . data_get($customer->options, 'savingsaccount.savingsaccounttype') . '</savingsaccounttype>' . PHP_EOL .
                '      </savingsaccount>' . PHP_EOL .
                '   </savingsaccounts>' . PHP_EOL .
                '   <loanaccounts/>' . PHP_EOL .
                '</p:getcustomerdetailsresponse>';
        }

        // todo: Add loan account to the details
        return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<p:getcustomerdetailsresponse xmlns:p="http://www.ericsson.com/em/emm/sp/backend" '
            . 'xmlns:iso="urn:iso:std:iso:20022:tech:xsd" '
            . 'xmlns:op="http://www.ericsson.com/em/emm/common" '
            . 'xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" '
            . 'xsi:schemaLocation="http://www.ericsson.com/em/emm/sp/backend savingandlendingbackend.xsd'
            . '">' . PHP_EOL .
            '   <status>REGISTERED</status>' . PHP_EOL .
            '   <savingsaccounts>' . PHP_EOL .
            '      <savingsaccount>' . PHP_EOL .
            '         <accountnumber>' . data_get($customer->options, 'savingsaccount.accountnumber') . '</accountnumber>' . PHP_EOL .
            '         <status>' . data_get($customer->options, 'savingsaccount.status') . '</status>' . PHP_EOL .
            '         <balance>' . PHP_EOL .
            '            <amount>' . data_get($customer->options, 'savingsaccount.balance.amount') . '</amount>' . PHP_EOL .
            '            <currency>' . data_get($customer->options, 'savingsaccount.balance.currency') . '</currency>' . PHP_EOL .
            '         </balance>' . PHP_EOL .
            '         <savingsaccounttype>' . data_get($customer->options, 'savingsaccount.savingsaccounttype') . '</savingsaccounttype>' . PHP_EOL .
            '      </savingsaccount>' . PHP_EOL .
            '   </savingsaccounts>' . PHP_EOL .
            '   <loanaccounts/>' . PHP_EOL .
            '</p:getcustomerdetailsresponse>';
    }

    public function bankDebitCompleted(Request $request): string
    {
        $details = $this->xmlToArray($request->getContent());

        $phoneNumber = str(Arr::get($details, 'fromfri'))->after('FRI:')->before('/')->toString();
        $customer = Customer::query()->firstWhere('Telephone_Number', $phoneNumber);
        $loan = $customer->activeLoans()->latest()->first();

        if (empty($loan)) {
            return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<bac:bankdebitcompletedresponse xmlns:bac="http://www.ericsson.com/em/emm/sp/backend">' . PHP_EOL .
                //'  <externaltransactionid>' . $transaction->Provider_TXN_ID . '</externaltransactionid>' . PHP_EOL .
                '  <status>FAILED</status>' . PHP_EOL .
                '</bac:bankdebitcompletedresponse>';
        }

        $transactionRecord = [
            'Partner_ID' => $loan->Partner_ID,
            'Type' => Transaction::REPAYMENT,
            'Amount' => data_get($details, 'amount.amount', 0),
            'Status' => 'Completed',
            'Telephone_Number' => $customer->Telephone_Number,
            'TXN_ID' => Transaction::generateID(),
            'Loan_ID' => $loan->id,
            'Loan_Application_ID' => $loan->Loan_Application_ID,
            'Provider_TXN_ID' => data_get($details, 'transactionid'),
            'Payment_Reference' => data_get($details, 'transactionid'),
        ];

        $transaction = new Transaction($transactionRecord);

        try {
            $transaction->save();

            // todo: Process repayment
            app(ProcessLoanRepaymentAction::class)->execute($transaction);

            return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<bac:bankdebitcompletedresponse xmlns:bac="http://www.ericsson.com/em/emm/sp/backend">' . PHP_EOL .
                '  <externaltransactionid>' . $transaction->Provider_TXN_ID . '</externaltransactionid>' . PHP_EOL .
                '  <status>SUCCESSFUL</status>' . PHP_EOL .
                '</bac:bankdebitcompletedresponse>';

        } catch (\Exception $e) {
            $transaction->Status = 'Failed';
            $transaction->save();

            return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<bac:bankdebitcompletedresponse xmlns:bac="http://www.ericsson.com/em/emm/sp/backend">' . PHP_EOL .
                '  <externaltransactionid>' . $transaction->TXN_ID . '</externaltransactionid>' . PHP_EOL .
                '  <status>FAILED</status>' . PHP_EOL .
                '</bac:bankdebitcompletedresponse>';
        }
    }

    private function xmlToArray(string $xml): array
    {
        // Implement XML to array conversion
        // This is a simplified example
        return json_decode(json_encode(simplexml_load_string($xml)), true);
    }

    /**
     * @return string
     */
    public function getLoanAccountTemplate(): string
    {
        return '   <loanaccounts>' . PHP_EOL .
            '      <loanaccount>' . PHP_EOL .
            '         <accountnumber>_accountNumber</accountnumber>' . PHP_EOL .
            '         <status>APPROVED</status>' . PHP_EOL .
            '         <due>' . PHP_EOL .
            '            <amount>_amount</amount>' . PHP_EOL .
            '            <currency>UGX</currency>' . PHP_EOL .
            '         </due>' . PHP_EOL .
            '         <duedate>_dueDate</duedate>' . PHP_EOL .
            '         <loantype>PERSONAL</loantype>' . PHP_EOL .
            '         <interest>0.0</interest>' . PHP_EOL .
            '         <extension>' . PHP_EOL .
            '           <BankWallet>WKD-PERSONAL</BankWallet>' . PHP_EOL .
            '         </extension>' . PHP_EOL .
            '      </loanaccount>' . PHP_EOL .
            '   </loanaccounts>';
    }
}
