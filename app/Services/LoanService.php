<?php

namespace App\Services;

use App\Actions\GetPartnerAppNameAction;
use App\Models\Partner;
use App\Models\Customer;
use App\Models\Transaction;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use App\Jobs\TransactableGetThroughPhoneJob;
use Exception;
use Illuminate\Support\Facades\DB;

class LoanService
{
    public static function initiateDisbursement(Partner $partner, Customer $customer, float $amount, $applicationId): bool
    {
        /**
         * This transaction must exist in case anything wrong happens at disbursement
         * This fixes the issue of a pending application without any transaction.
         */
        $transaction = Transaction::create([
            'Partner_ID' => $partner->id,
            'Type' => Transaction::DISBURSEMENT,
            'Amount' => $amount,
            'Status' => 'Pending',
            'Telephone_Number' => $customer->Telephone_Number,
            'TXN_ID' => Transaction::generateID(),
            'Provider_TXN_ID' => app()->isLocal() ? Transaction::generateID() : null,
            'Loan_Application_ID' => $applicationId
        ]);

        try {
            DB::beginTransaction();

            $payment = (new PaymentServiceManager($transaction))->paymentService;

            $response = $payment->disburse($customer->Telephone_Number, $transaction->Amount, $transaction->TXN_ID, Transaction::DISBURSEMENT);
            $responseStatus = Arr::get($response, 'message'); // SUCCEEDED | PENDING | FAILED

            $updateDetails = [
                'Provider_TXN_ID' => Arr::get($response, 'reference'),
                'Payment_Service_Provider' => Arr::get($response, 'service_provider'),
            ];

            if ($responseStatus === 'FAILED') {
                $updateDetails['Status'] = 'Failed';
                $updateDetails['Payment_Reference'] = Arr::get($response, 'payment_reference');
                $updateDetails['Narration'] = Arr::get($response, 'payment_message');
                $transaction->update($updateDetails);
                DB::commit();
                return false;
            }

            $transaction->update($updateDetails);
            DB::commit();
            return true;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage());
            return false;
        }
    }


    public static function initiatRepayment(Partner $partner, Customer $customer, float $amount, $loan)
    {
        // Determine the service to initiate the request to.
        // Options:
        // Yo
        // Airtel
        // MTN,
        // Bank Corebanking
        $transaction_request = Transaction::create([
            'Partner_ID' => $partner->id,
            'Type' => Transaction::REPAYMENT,
            'Amount' => $amount,
            'Status' => 'Pending',
            'Telephone_Number' => $customer->Telephone_Number,
            'TXN_ID' => Transaction::generateID(),
            'Loan_ID' => $loan->id,
            'Loan_Application_ID' => $loan->application->id,
            'Provider_TXN_ID' => app()->isLocal() ? Transaction::generateID() : null,
        ]);
        TransactableGetThroughPhoneJob::dispatch($transaction_request, Transaction::REPAYMENT);
    }

    public static function initiateDownpayment(Partner $partner, Customer $customer, float $amount, $loanApplication)
    {
        $transaction = Transaction::where('Type', Transaction::DOWNPAYMENT)->where('Loan_Application_ID', $loanApplication->id)->latest()->first();
        if ($transaction) {
            throw new Exception('This transaction was already created for this application go ahead and pay');
        }

        $transaction_request = Transaction::create([
            'Partner_ID' => $partner->id,
            'Type' => Transaction::DOWNPAYMENT,
            'Amount' => $amount,
            'Status' => 'Pending',
            'Telephone_Number' => $customer->Telephone_Number,
            'TXN_ID' => Transaction::generateID(),
            'Loan_Application_ID' => $loanApplication->id,
            'Asset_Provider' => 'SPIRO',
            'Provider_TXN_ID' => app()->isLocal() ? Transaction::generateID() : null,
            'Asset_Disbursement_Status' => 'Pending', // Fixes bug where the down payments do not show up on the list.
        ]);
        TransactableGetThroughPhoneJob::dispatch($transaction_request, Transaction::DOWNPAYMENT);
    }

    public static function initiateRestructure(float $amount, $loan, $days)
    {
        $transactionType = Transaction::RESTRUCTURE;
        // $transaction = Transaction::where('Type', $transactionType)->where('Loan_ID', $loan->id)->latest()->first();
        // if ($transaction) {
        //     throw new Exception('This transaction was already created for this application go ahead and pay');
        // }

        $transaction_request = Transaction::create([
            'Partner_ID' => $loan->partner->id,
            'Type' => $transactionType,
            'Amount' => $amount,
            'Status' => 'Pending',
            'Telephone_Number' => $loan->customer->Telephone_Number,
            'TXN_ID' => (string) Transaction::generateID(),
            'Loan_ID' => $loan->id,
            'Restructure_Days' => $days,
            'Provider_TXN_ID' => app()->isLocal() ? Transaction::generateID() : null,
        ]);
        TransactableGetThroughPhoneJob::dispatch($transaction_request, $transactionType);
    }
}
