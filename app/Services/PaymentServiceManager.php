<?php

namespace App\Services;

use App\Models\PartnerOva;
use App\Models\Transaction;
use App\Services\Contracts\ProvidesTransactableAPIs;
use App\Services\Factories\PaymentServiceFactory;
use App\Exceptions\PaymentServiceException;
use App\Models\LoanProduct;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class PaymentServiceManager
{
  public ProvidesTransactableAPIs $paymentService;
  protected array $appConfiguration = [];
  public string $environment = 'test';
  public string $paymentProviderName = '';

  /**
   * @throws PaymentServiceException
   */
  public function __construct(private readonly Transaction $transaction)
  {
    try {
      $this->initializePaymentService();
    } catch (\Exception $e) {
      Log::error('Payment service initialization failed', [
        'transaction_id' => $transaction->id,
        'error' => $e->getMessage()
      ]);
      throw $e;
    }
  }

  /**
   * @throws PaymentServiceException
   * @throws \Exception
   */
  protected function initializePaymentService(): void
  {
    $loanProduct = $this->getLoanProduct();
    $switch = $this->getPaymentSwitch($loanProduct);

    $this->environment = strtolower($switch->environment);
    $this->paymentProviderName = strtolower($switch->name);

    $this->getOvaSettings();
    $this->getPaymentService();
  }

  protected function getLoanProduct()
  {
      if (is_null($this->transaction->Loan_ID) || is_null($this->transaction->Loan_Application_ID)) {
          return new LoanProduct();
      }

      if ($this->transaction->loan) {
          return $this->transaction->loan->loan_product;
      } elseif ($this->transaction->loanApplication) {
          return $this->transaction->loanApplication->loan_product;
      }

      return new LoanProduct();
  }

  /**
   * @throws PaymentServiceException
   */
  protected function getPaymentSwitch($loanProduct)
  {
    $switch = $loanProduct->switch;

    if (!$switch) {
      $switch = $this->transaction->partner
        ->switches()
        ->firstWhere(['category' => 'Payment', 'Status' => 'On']);
    }

    if (!$switch) {
      throw new PaymentServiceException(PaymentServiceException::NO_PAYMENT_GATEWAY);
    }

    return $switch;
  }

  /**
   * @throws PaymentServiceException
   */
  protected function getOvaSettings(): self
  {
    $cacheKey = "ova_settings_{$this->transaction->Partner_ID}_{$this->paymentProviderName}";

    $this->appConfiguration = Cache::remember($cacheKey, now()->addHours(24), function () {
      $ova = PartnerOva::query()->firstWhere([
        'partner_id' => $this->transaction->Partner_ID,
      ]);

      if (empty($ova)) {
        throw new PaymentServiceException(PaymentServiceException::NO_OVA_ACCOUNT);
      }

      $config = [
        'yo' => $ova->only(['yo_username', 'yo_password']),
        'airtel' => $ova->only(['airtel_public_key', 'pin', 'client_key', 'client_secret']),
        'mtn' => $ova->only(['mtn_url', 'mtn_callback']),
      ][$this->paymentProviderName] ?? [];

      if (count(array_filter($config)) === 0) {
        throw new PaymentServiceException(PaymentServiceException::EMPTY_CREDENTIALS);
      }

      return $config;
    });

    $this->validateConfiguration();
    return $this;
  }

  protected function validateConfiguration(): void
  {
    $requiredConfig = [
      'yo' => ['yo_username', 'yo_password'],
      'airtel' => ['airtel_public_key', 'pin', 'client_key', 'client_secret'],
      'mtn' => ['mtn_url']
    ];

    $required = $requiredConfig[$this->paymentProviderName] ?? [];
    $missing = array_diff($required, array_keys($this->appConfiguration));

    if (!empty($missing)) {
      throw new PaymentServiceException(
        "Missing required configuration: " . implode(', ', $missing)
      );
    }
  }

  protected function getPaymentService(): self
  {
    try {
      $this->paymentService = PaymentServiceFactory::create(
        $this->paymentProviderName,
        $this->environment,
        $this->appConfiguration
      );

      return $this;
    } catch (\Exception $e) {
      Log::error('Failed to initialize payment service', [
        'provider' => $this->paymentProviderName,
        'environment' => $this->environment,
        'error' => $e->getMessage()
      ]);
      throw $e;
    }
  }
}
