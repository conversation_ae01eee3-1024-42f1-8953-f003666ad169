<?php

namespace App\Console\Commands\MTNTests;

use App\Actions\CreateTestTransactionAction;
use App\Services\MockMtnApiService;
use App\Exceptions\MtnApiException;
use App\Services\PaymentServiceManager;
use Illuminate\Console\Command;

class Collect extends Command
{
    protected $signature = 'mtn:collect {--partner= : Partner ID for this transaction}';
    protected $description = 'Test MTN collect money from agent API';

    public function handle(): int
    {
        try {
            $msisdn = $this->askValid(
                'Enter agent phone number (format: 256XXXXXXXXX)',
                'msisdn',
                ['required', 'string', 'regex:/^256[0-9]{9}$/'],
                'The phone number must be in the format 256XXXXXXXXX'
            );

            $amount = $this->askValid(
                'Enter amount to collect',
                'amount',
                ['required', 'numeric', 'min:0.01'],
                'Amount must be a positive number'
            );

            $reference = $this->askValid(
                'Enter transaction reference',
                'reference',
                ['required', 'string'],
                'Reference is required'
            );

            $message = $this->ask(
                'Enter message to show to agent (default: Loan repayment)',
                'Repayment'
            );

            $this->info("\nCollection Details:");
            $this->line("Agent MSISDN: {$msisdn}");
            $this->line("Amount: {$amount}");
            $this->line("Reference: {$reference}");
            $this->line("Message: {$message}");
            if (!$this->confirm('Do you want to proceed with the collection?', true)) {
                $this->info('Collection cancelled.');
                return 0;
            }

            $transaction = app(CreateTestTransactionAction::class)->execute(
                $amount,
                $msisdn,
                $this->option('partner'),
            );

            $mtnService = (new PaymentServiceManager($transaction))->paymentService;
            $response = $mtnService->collect($msisdn, $amount, $reference, $message);

            $this->info('Response:');
            $this->line(json_encode($response, JSON_PRETTY_PRINT));

            return 0;
        } catch (MtnApiException $e) {
            $this->error('MTN API Error:');
            $this->error($e->getMessage());

            if (config('services.mtn.debug')) {
                $this->error('Stack trace:');
                $this->error($e->getTraceAsString());
            }

            return 1;
        } catch (\Exception $e) {
            $this->error('Unexpected Error:');
            $this->error($e->getMessage());

            if (config('services.mtn.debug')) {
                $this->error('Stack trace:');
                $this->error($e->getTraceAsString());
            }

            return 1;
        }
    }

    /**
     * Ask for input with validation
     *
     * @param string $question
     * @param string $field
     * @param array $rules
     * @param string|null $errorMessage
     * @return string
     */
    private function askValid(string $question, string $field, array $rules, ?string $errorMessage = null): string
    {
        do {
            $value = $this->ask($question);
            $validator = validator([$field => $value], [$field => $rules]);

            if ($validator->fails()) {
                $this->error($errorMessage ?? $validator->errors()->first());
                continue;
            }

            return $value;
        } while (true);
    }
}
