<?php

namespace App\Console\Commands\MTNTests;

use App\Actions\CreateTestTransactionAction;
use App\Exceptions\MtnApiException;
use App\Services\MockMtnApiService;
use Illuminate\Console\Command;
use App\Services\PaymentServiceManager;

class DisburseLoan extends Command
{
    protected $signature = 'mtn:disburse
                          {msisdn : Customer phone number (format: 256XXXXXXXXX)}
                          {amount : Amount to disburse}
                          {--partner= : Partner ID to attach this transaction to}
                          {--reference= : Optional transaction reference (random if not provided)}';

    protected $description = 'Test MTN loan disbursement API';

    public function handle(): int
    {
        try {
            $transaction = app(CreateTestTransactionAction::class)->execute(
                $this->argument('amount'),
                $this->argument('msisdn'),
                $this->option('partner'),
            );

            $msisdn = $this->argument('msisdn');
            $amount = (int) $this->argument('amount');
            $reference = $this->option('reference') ?? uniqid('MTN_TEST_');

            $this->info("Testing loan disbursement...");
            $this->line("MSISDN: $msisdn");
            $this->line("Amount: $amount");
            $this->line("Reference: $reference");

            $api = (new PaymentServiceManager($transaction))->paymentService;
            $response = $api->disburse($transaction->Telephone_Number, $transaction->Amount, $transaction->TXN_ID);

            $this->info('Response:');
            $this->line(json_encode($response, JSON_PRETTY_PRINT));

            return 0;
        } catch (MtnApiException $e) {
            $this->error('MTN API Error:');
            $this->error($e->getMessage());
            $this->error('Stack trace:');
            $this->error($e->getTraceAsString());
            return 1;
        } catch (\Exception $e) {
            $this->error('Unexpected Error:');
            $this->error($e->getMessage());
            $this->error('Stack trace:');
            $this->error($e->getTraceAsString());
            return 1;
        }
    }
}
