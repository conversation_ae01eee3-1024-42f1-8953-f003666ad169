<?php

namespace App\Console\Commands;

use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\LoanDisbursement;
use App\Models\Partner;
use App\Models\Transaction;
use App\Services\FinacleService;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class PostDisbursementToPartner extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lms:post-disbursement-to-partner';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $partner = Partner::where('Identification_Code', 'CI009')->firstOrFail();
        $disbursements = LoanDisbursement::where('partner_id', $partner->id)->where('partner_notified', false)->get();
        foreach ($disbursements as $disbursement) {
            $applicationId = $disbursement->loan->application->id;
            $transaction = Transaction::where('Type', 'Disbursement')->where('Loan_Application_ID', $applicationId)->first();
            // if (!$transaction) {
            //     continue;
            // }
            $result = $this->postDisbursementToPartner((int)$disbursement->amount, $disbursement->customer->Telephone_Number, $disbursement->customer->name, $disbursement->loan_id, $transaction->Payment_Reference);
            $this->info(json_encode($result));
            if ($result['success']) {
                $disbursement->partner_notified = true;
                $disbursement->partner_notified_date = Carbon::now();
                $disbursement->save();
                $this->info('Partner has been notified of this loan disbursement');
            }
        }
    }

    // todo: Handle posting to ABC in a job to avoid blocking the request.
    private function postDisbursementToPartner($amount, $phone, $name, $loanId, $transactionReference)
    {
        $finacleService = new FinacleService();
        $result = $finacleService->executeLoanDisbursement(
            $amount,
            $phone,
            $name,
            Carbon::now()->format('Y-m-d\TH:i:s.v'),
            $loanId,
            $transactionReference,
        );
        return $result;
    }
}
