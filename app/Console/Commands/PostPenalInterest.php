<?php

namespace App\Console\Commands;

use App\Models\Accounts\Account;
use App\Models\JournalEntry;
use App\Models\Loan;
use App\Models\LoanDisbursement;
use App\Models\Partner;
use App\Models\Transaction;
use App\Services\Account\AccountSeederService;
use App\Services\FinacleService;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class PostPenalInterest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lms:post-penal-interest';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $partner = Partner::where('Identification_Code', 'CI009')->firstOrFail();
            // Get the interest receivables account
            // Get the penalties receivables account
            $penaltiesReceivablesAccount = Account::where("partner_id", $partner->id)
                ->where("slug", AccountSeederService::PENALTIES_RECEIVABLES_SLUG)
                ->first();
            if (!$penaltiesReceivablesAccount) {
                throw new Exception('No penalties receivable account found for partner ' . $partner->Institution_Name);
            }
            $dailyInterestTotal = JournalEntry::whereDate('created_at', today())->where('account_id', $penaltiesReceivablesAccount->id)->sum('amount');

            $this->info("Total penal interest posted today: " . $dailyInterestTotal);
            $result = $this->postPenalInterestBooking($dailyInterestTotal);
            $this->info(json_encode($result));
            if ($result['success']) {
                $this->info('Partner has been notified of this normal interest booking');
            }
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }

    // // todo: Handle posting to ABC in a job to avoid blocking the request.
    private function postPenalInterestBooking($amount)
    {
        $finacleService = new FinacleService();
        $transactionReference = Transaction::generateID();
        $result = $finacleService->executePenalInterestBooking(
            $amount,
            Carbon::now()->format('Y-m-d\TH:i:s.v'),
            $transactionReference
        );
        return $result;
    }
}
