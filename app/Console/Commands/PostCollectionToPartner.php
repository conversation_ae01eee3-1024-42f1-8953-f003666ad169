<?php

namespace App\Console\Commands;

use App\Models\Loan;
use App\Models\LoanDisbursement;
use App\Models\LoanRepayment;
use App\Models\Partner;
use App\Models\Transaction;
use App\Services\FinacleService;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;

class PostCollectionToPartner extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lms:post-collection-to-partner';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $partner = Partner::where('Identification_Code', 'CI009')->firstOrFail();
        $repayments = LoanRepayment::where('partner_id', $partner->id)->where('partner_notified', false)->get();
        foreach ($repayments as $repayment) {
            $applicationId = $repayment->loan->application->id;
            $transaction = Transaction::where('Type', 'Repayment')->where('Loan_Application_ID', $applicationId)->latest()->first();
            $result = $this->postCollectionToPartner((int)$repayment->amount, $repayment->customer->Telephone_Number, $repayment->customer->name, $repayment->Loan_ID, $transaction->Payment_Reference);
            $this->info(json_encode($result));
            if ($result['success']) {
                $repayment->partner_notified = true;
                $repayment->partner_notified_date = Carbon::now();
                $repayment->save();
                $this->info('Partner has been notified of this loan repayment');
            }
        }
    }

    // todo: Handle posting to ABC in a job to avoid blocking the request.
    private function postCollectionToPartner($amount, $phone, $name, $loanId, $transactionReference)
    {
        $finacleService = new FinacleService();
        $result = $finacleService->executeLoanCollection(
            $amount,
            $phone,
            $name,
            Carbon::now()->format('Y-m-d\TH:i:s.v'),
            $loanId,
            $transactionReference
        );
        return $result;
    }
}
