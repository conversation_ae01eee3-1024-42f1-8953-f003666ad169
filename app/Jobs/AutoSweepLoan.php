<?php

namespace App\Jobs;

use App\Models\Loan;
use App\Models\Transaction;
use App\Services\PaymentServiceManager;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class AutoSweepLoan implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(protected int $loanId)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $loan = Loan::query()->findOrFail($this->loanId);

            $transactionRecord = [
                'Partner_ID' => $loan->Partner_ID,
                'Type' => Transaction::REPAYMENT,
                'Amount' => $loan->totalOutstandingBalance(),
                'Status' => 'Pending',
                'Telephone_Number' => $loan->customer->Telephone_Number,
                'TXN_ID' => Transaction::generateID(),
                'Loan_ID' => $loan->id,
                'Loan_Application_ID' => $loan->Loan_Application_ID,
                'Provider_TXN_ID' => null,
                'Payment_Reference' => null,
            ];

            $transaction = new Transaction($transactionRecord);

            $transaction->save();

            $api = (new PaymentServiceManager($transaction))->paymentService;
            $response = $api->autoCollect($transaction);

            Log::info(json_encode($response));
        } catch (\Throwable $th) {
            Log::error($th->getMessage());
        }
    }
}
